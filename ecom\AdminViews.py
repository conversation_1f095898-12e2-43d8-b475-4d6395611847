from django.shortcuts import get_object_or_404
from django.db import transaction
from django.db.models import Q, Sum
from django.http import JsonResponse
from django.db import transaction
import logging
from django.db import transaction
from django.urls import reverse
from django.shortcuts import get_object_or_404
from django.db import transaction
from django.db.models import Q
from django.http import JsonResponse
from django.shortcuts import render
from django.urls import reverse
from django.db import transaction
from django.http import HttpResponseRedirect
from django.db import transaction
from django.shortcuts import get_object_or_404
from django.http import JsonResponse
import logging
from django.db.models import Q
from django.core.paginator import Paginator
from django.urls import reverse
from django.http import HttpResponseBadRequest
from django.shortcuts import get_object_or_404, redirect
from django.apps import apps
import re
from django.core.cache import cache
from datetime import datetime
import itertools, openpyxl, csv, io
import platform
from django.apps import apps
from django.forms import formset_factory, inlineformset_factory
from django.shortcuts import get_object_or_404, render,redirect
from ecommerce import settings
from . import forms,models
from .forms import *
from .models import Orders, OrderProduct, Product, Batch_exp, Customer, Supplier, PriceList, Quantity, validate_file_size
from .forms import SupplierForm
from django.http import Http404, HttpResponseBadRequest, HttpResponseRedirect,HttpResponse, JsonResponse
from django.contrib.auth.models import Group
from django.contrib.auth.decorators import user_passes_test
import json, decimal, os, re, pdfkit
from django.core.exceptions import ValidationError
from django.utils.encoding import smart_str
from django.contrib import messages
from django.template.loader import get_template
from collections import defaultdict
from num2words import num2words
import pandas as pd
from decimal import Decimal, InvalidOperation



# def adminclick_view(request):
#     if request.user.is_authenticated:
#         return HttpResponseRedirect('afterlogin')
#     return HttpResponseRedirect('login')

def is_admin(user):
    return user.is_staff or user.groups.filter(name='Staff').exists()


logger = logging.getLogger(__name__)

@user_passes_test(is_admin, login_url='login')
def admin_dashboard_view(request):
    try:
        # for cards on dashboard
        customercount = cache.get_or_set('customercount', models.Customer.objects.count, timeout=300)
        productcount = cache.get_or_set('productcount', models.Product.objects.count, timeout=300)
        ordercount = cache.get_or_set('ordercount', models.Orders.objects.count, timeout=300)
    except models.Customer.DoesNotExist:
        customercount = 0
    except models.Product.DoesNotExist:
        productcount = 0
    except models.Orders.DoesNotExist:
        ordercount = 0

    # for recent order tables
    orders = models.Orders.objects.select_related('customer').prefetch_related('orderproduct_set').all()
    ordered_products = [[] for _ in orders]
    ordered_bys = models.Orders.objects.select_related('customer').values_list('customer', flat=True)

    mydict = {
        'customercount': customercount,
        'productcount': productcount,
        'ordercount': ordercount,
        'orders': orders,
        'ordered_products': ordered_products,
        'ordered_bys': ordered_bys,
    }

    return render(request, 'ecom/Admin/admin_dashboard.html', context=mydict)

@user_passes_test(is_admin, login_url='login')
def delete_view(request, pk):
    pattern = r'/delete-(?P<model>\w+)/(?P<pk>\d+)'
    match = re.match(pattern, request.path)
    if match:
        model = match.group('model')

        model_class = apps.get_model(app_label='ecom', model_name=model)
        instance = get_object_or_404(model_class, pk=pk)
        instance.delete()

        return redirect('admin-dashboard')
    else:
        return HttpResponseBadRequest("Invalid URL pattern")

# make sure the autocomplete fields have name or id defined
# from dal import autocomplete
# class ProductAutocomplete(autocomplete.Select2QuerySetView):
#     def get_queryset(self):
#         qs = models.Batch_exp.objects.filter(active=True)

#         if self.q:
#             qs = qs.filter(product__icontains=self.q)

#         return qs

#---------------------------------------------------------------------------------
#------------------------ CUSTOMER RELATED VIEWS START ---------------------------
#---------------------------------------------------------------------------------
@user_passes_test(is_admin, login_url='login')
def view_customer_view(request):
    if request.method == 'POST':
        query = request.POST.get('q')
        if query:
            customers = models.Customer.objects.select_related('user').filter(Q(user__first_name__icontains=query))
        else:
            customers = models.Customer.objects.select_related('user').all()
    else:
        customers = models.Customer.objects.select_related('user').all()

    # paginator = Paginator(customers, 20)  # Show 10 customers per page
    # page_number = request.GET.get('page')
    # page_obj = paginator.get_page(page_number)

    #setup paginator at frontend

    return render(request, 'ecom/Admin/view_customer.html', {'customers': customers})




@user_passes_test(is_admin, login_url='login')
def update_customer_view(request,pk):
    customer = get_object_or_404(models.Customer, id=pk)
    user = get_object_or_404(models.User, id=customer.user_id)
    userForm = CustomerUserForm(instance=user)
    customerForm = CustomerForm(instance=customer)
    mydict = {'userForm': userForm, 'customerForm': customerForm}
    if request.method == 'POST':
        userForm = CustomerUserForm(request.POST, instance=user)
        customerForm = CustomerForm(request.POST, instance=customer)
        if userForm.is_valid() and customerForm.is_valid():
            with transaction.atomic():
                # Get the cleaned data
                user_data = userForm.cleaned_data

                # Only update username if provided
                if user_data.get('username'):
                    user.username = user_data['username']

                # Only update password if provided
                if user_data.get('password'):
                    user.set_password(user_data['password'])

                # Always update first_name
                user.first_name = user_data['first_name']

                # Save the user
                user.save()

                # Save the customer form
                customerForm.save()

            return redirect('view-customer')
        else:
            logging.error(customerForm.errors)
            return HttpResponse(userForm.errors)
    return render(request, 'ecom/Admin/admin_update_customer.html', context=mydict)


@user_passes_test(is_admin, login_url='login')
def create_customer_view(request):
    userForm = CustomerUserForm()
    customerForm = CustomerForm()
    mydict = {'userForm': userForm, 'customerForm': customerForm}
    if request.method == 'POST':
        userForm = CustomerUserForm(request.POST)
        customerForm = CustomerForm(request.POST)
        if userForm.is_valid() and customerForm.is_valid():
            with transaction.atomic():
                user = userForm.save(commit=False)
                user.set_password(user.password)
                user.save()
                customer = customerForm.save(commit=False)
                customer.user = user
                customer.save()
                my_customer_group = Group.objects.get_or_create(name='CUSTOMER')
                my_customer_group[0].user_set.add(user)
        else:
            return JsonResponse({'errors': str(userForm.errors) + str(customerForm.errors)})
        return HttpResponseRedirect(reverse('view-customer'))
    return render(request, 'ecom/Admin/create_customer.html', context=mydict)



#---------------------------------------------------------------------------------
#------------------------ Supplier RELATED VIEWS START ---------------------------
#---------------------------------------------------------------------------------

@user_passes_test(is_admin, login_url='login')
def view_supplier_view(request):
    if request.method == 'POST':
        query = request.POST.get('q')
        suppliers = models.Supplier.objects.filter(Q(name__icontains=query))
    else:
        suppliers = models.Supplier.objects.all()

    return render(request, 'ecom/Admin/view_supplier.html', {'suppliers': suppliers})




@user_passes_test(is_admin, login_url='login')
def update_supplier_view(request, pk):
    supplier = get_object_or_404(models.Supplier, id=pk)
    supplier_form = SupplierForm(instance=supplier)

    if request.method == 'POST':
        supplier_form = SupplierForm(request.POST, instance=supplier)
        if supplier_form.is_valid():
            with transaction.atomic():
                supplier_form.save()
            messages.success(request, f'Supplier "{supplier.name}" updated successfully!')
            return redirect('view-supplier')
        else:
            messages.error(request, 'Please correct the errors below.')
            logger.error(f"Supplier form validation failed: {supplier_form.errors}")
    return render(request, 'ecom/Admin/admin_update_supplier.html', {'supplierForm': supplier_form})




@user_passes_test(is_admin, login_url='login')
def add_supplier_view(request):
    supplier_form = SupplierForm()

    if request.method == 'POST':
        supplier_form = SupplierForm(request.POST)
        if supplier_form.is_valid():
            with transaction.atomic():
                supplier = supplier_form.save()
                messages.success(request, f'Supplier "{supplier.name}" created successfully!')
            return HttpResponseRedirect(reverse('view-supplier'))
        else:
            messages.error(request, 'Please correct the errors below.')
            logger.error(f"Supplier form validation failed: {supplier_form.errors}")
    return render(request, 'ecom/Admin/create_supplier.html', {'supplierForm': supplier_form})


#---------------------------------------------------------------------------------
#------------------------ Orders RELATED VIEWS START ---------------------------
#---------------------------------------------------------------------------------

@user_passes_test(is_admin, login_url='login')
def admin_booking_view(request):
    orders = models.Orders.objects.all().prefetch_related('customer')
    form = OrderFilterForm(request.POST or None)
    # Add archive filter to form
    form.fields['show_archived'] = frm.BooleanField(required=False, initial=False)

    if request.method == 'POST' and form.is_valid():
        filters = Q()
        if form.cleaned_data.get('from_date'):
            filters &= Q(date__gte=form.cleaned_data['from_date'])
        if form.cleaned_data.get('to_date'):
            filters &= Q(date__lte=form.cleaned_data['to_date'])
        if form.cleaned_data.get('customer'):
            filters &= Q(customer=form.cleaned_data['customer'])
        if form.cleaned_data.get('status'):
            filters &= Q(status=form.cleaned_data['status'])
        if form.cleaned_data.get('invoice'):
            filters &= Q(invoice__contains=form.cleaned_data['invoice'])

        # Handle archived filter
        if not form.cleaned_data.get('show_archived'):
            filters &= Q(is_archived=False)

        orders = orders.filter(filters)
        return render(request, 'ecom/Admin/admin_view_booking.html', {'data': orders, 'filterForm': form})

    # By default, only show non-archived orders
    orders = orders.filter(is_archived=False).order_by('-invoice')

    paginator = Paginator(orders, 100)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)

    return render(request, 'ecom/Admin/admin_view_booking.html', {'data': page_obj, 'filterForm': form})


# deep review required

logger = logging.getLogger(__name__)



# def update_order_view(request, pk):
#     order = get_object_or_404(models.Orders, id=pk)
#     orderForm = OrderForm(instance=order)
#     orderProducts = models.OrderProduct.objects.filter(order=order).select_related('product', 'pack').prefetch_related('batch_exp')
#     orderProduct_formset = inlineformset_factory(models.Orders, models.OrderProduct, form=admin_OrderProductForm, extra=1)

#     if request.method == 'POST':
#         orderForm = OrderForm(request.POST, instance=order)
#         formset = orderProduct_formset(request.POST, instance=order)

#         if orderForm.is_valid():
#             error_message = order.process_status_change(orderForm.cleaned_data['status'])
#             if error_message:
#                 return HttpResponse(error_message)

#             orderForm.save()
#             return redirect('admin-view-booking')

#         elif formset.is_valid():
#             existing_order_products = {op.id: op for op in orderProducts}
#             for form in formset:
#                 if form.cleaned_data:
#                     order_product = form.save(commit=False)
#                     if order_product.id in existing_order_products:
#                         existing_order_products[order_product.id].quantity = order_product.quantity
#                         existing_order_products[order_product.id].save()
#                     else:
#                         order_product.order = order
#                         order_product.save()

#             order.total = orderProducts.aggregate(Sum('amount'))['amount__sum'] or 0
#             order.save()
#             return redirect('admin-view-booking')

#         else:
#             return HttpResponse(formset.errors)

#     else:
#         formset = orderProduct_formset(instance=order)

#     return render(request, 'ecom/Admin/update_order.html', {'orderForm': orderForm, 'formset': formset})


@user_passes_test(is_admin, login_url='login')
def admin_update_options(request):
    if request.method == 'POST':
        body_unicode = request.body.decode('utf-8')
        data = json.loads(body_unicode)
        order_id = data.get('order')
        with transaction.atomic():
            order = get_object_or_404(models.Orders, invoice=order_id)
            products = models.OrderProduct.objects.filter(order=order)
            product = models.Product.objects.get(item=data.get('selectedOption'))

            products.filter(Q(product__item=data.get('productName'))).update(product=product, batch_exp=None)

        return JsonResponse({'redirect_url': str(order.id)})

#not optimized
# from weasyprint import HTML, CSS
def render_to_pdf(template_src, context_dict, save):
    template = get_template(template_src)
    html  = template.render(context_dict)

    # pdf_file = HTML(string=html).write_pdf()
    # response = HttpResponse(pdf_file, content_type='application/pdf')
    # response['Content-Disposition'] = 'filename="home_page.pdf"'
    # return response

    #return HttpResponse(html)
    #pdf = pdfkit.from_string(html, False, configuration=config)

    if platform.system() == 'Linux':
        # Linux configuration
        pdf = pdfkit.from_string(html, False)
    else:
        # Windows configuration
        config = pdfkit.configuration(wkhtmltopdf=r'C:\Program Files\wkhtmltopdf\bin\wkhtmltopdf.exe')
        pdf = pdfkit.from_string(html, False, configuration=config)


    if save:
        return pdf
    else:
        response = HttpResponse(pdf, content_type='application/pdf')
    #response['Content-Disposition'] = 'attachment; filename="output.pdf"'
        return response


def download_invoice(order, save):
    from decimal import Decimal
    orderProducts = order.orderproduct_set.all()
    gst_totals = defaultdict(lambda: {'taxable_amount': 0, 'cgst': 0, 'sgst': 0})
    discount = 0
    length = 0

    # Calculate totals for each GST percentage
    for order_product in orderProducts:
        length += 1
        # Calculate actual discount amount (price * quantity * discount%)
        item_discount = order_product.price * order_product.quantity * Decimal(order_product.discount) / Decimal(100)
        discount += item_discount

        gst_percentage = order_product.gst_rate
        taxable_amount = order_product.amount
        cgst_percentage = sgst_percentage = gst_percentage / 2
        cgst_amount = sgst_amount = order_product.gst / 2
        gst_totals[gst_percentage]['taxable_amount'] += taxable_amount
        gst_totals[gst_percentage]['cgst'] += cgst_amount
        gst_totals[gst_percentage]['sgst'] += sgst_amount

    # Create the nested list format
    gst_data = []
    cgst = 0
    for gst_percentage, totals in gst_totals.items():
        taxable_amount = totals['taxable_amount']
        cgst_percentage = sgst_percentage = gst_percentage / 2
        cgst_amount = totals['cgst']
        cgst += cgst_amount
        sgst_amount = totals['sgst']
        igst = 0
        amount = cgst_amount + sgst_amount
        gst_data.append([taxable_amount, cgst_percentage, cgst_amount, sgst_percentage, sgst_amount, gst_percentage, igst, amount])

    # Print or return the nested list
    total_tax = cgst * 2
    total_amoun = order.amount
    total_amount = round(total_amoun)
    roundoff = total_amoun - total_amount
    total_words = num2words(total_amount)

    #filling in empty cells to keep format intact
    while len(gst_data)<3:
        gst_data.append([])

    limit = bool(length >= 7)

    mydict={
        'order' : order,
        'items' : orderProducts,
        'gst_data' : gst_data,
        'discount' : discount,
        'cgst' : cgst,
        'total_tax' : total_tax,
        'total_amount' : total_amount,
        'roundoff' : roundoff,
        'total_words' : total_words,
        'new_page': limit,
    }

    try:
        return render_to_pdf('ecom/Admin/invoice.html', mydict, save)
    except Exception as e:
        logger.error(f"Failed to generate PDF: {e}")
        return HttpResponse("An error occurred while generating the PDF.", status=500)


#*()*

@user_passes_test(is_admin, login_url='login')
def view_order_view(request,pk):
    orders=models.Orders.objects.get(id=pk)
    save = False

    return download_invoice(orders,save)


def generate_invoice_number(trade):
    if trade=='sales':
        last_order = models.Orders.objects.order_by('invoice').last()
        if not last_order:
            new_int = 1
        else:
            last_int = last_order.invoice
            new_int = last_int + 1
        return new_int
    else:
        last_order = models.Purchase.objects.order_by('inv').last()
        if not last_order:
            new_int = 1
        else:
            last_int = last_order.inv
            new_int = last_int + 1
        return new_int








#---------------------test order views--------------------------------

from django.views.generic import ListView, CreateView, UpdateView, DeleteView
from django.contrib.auth.mixins import LoginRequiredMixin
from django.db import transaction
from django.views.decorators.http import require_POST




class OrderListView(LoginRequiredMixin, ListView):
    model = Orders
    template_name = 'ecom/Admin/Ordering/order_list.html'
    context_object_name = 'orders'
    ordering = ['-date', '-invoice']
    paginate_by = 25

    def get_queryset(self):
        queryset = super().get_queryset()

        # Filter by date range if provided
        start_date = self.request.GET.get('start_date')
        end_date = self.request.GET.get('end_date')
        if start_date and end_date:
            queryset = queryset.filter(date__range=[start_date, end_date])

        # Filter by customer if provided
        customer_id = self.request.GET.get('customer')
        if customer_id:
            queryset = queryset.filter(customer_id=customer_id)

        # Filter by status if provided
        status = self.request.GET.get('status')
        if status:
            queryset = queryset.filter(status=status)

        # Filter by invoice if provided
        invoice = self.request.GET.get('invoice')
        if invoice:
            queryset = queryset.filter(invoice=invoice)

        # Filter by product if provided
        product_id = self.request.GET.get('product')
        if product_id:
            queryset = queryset.filter(orderproduct__product_id=product_id).distinct()

        # Show only non-archived by default, unless explicitly asked to show archived
        show_archived = self.request.GET.get('show_archived') == 'true'
        if not show_archived:
            queryset = queryset.filter(is_archived=False)

        return queryset

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['customers'] = Customer.objects.all()
        context['products'] = Product.objects.all().order_by('item')
        context['status_choices'] = Orders.StatusChoices.choices
        # Add any filters back to context
        context['filters'] = {
            'start_date': self.request.GET.get('start_date', ''),
            'end_date': self.request.GET.get('end_date', ''),
            'customer': self.request.GET.get('customer', ''),
            'status': self.request.GET.get('status', ''),
            'invoice': self.request.GET.get('invoice', ''),
            'product': self.request.GET.get('product', ''),
            'show_archived': self.request.GET.get('show_archived', '') == 'true',
        }
        return context


@user_passes_test(is_admin, login_url='login')
@transaction.atomic
def create_order(request):
    if request.method == 'POST':
        form = OrderForm(request.POST)
        formset = OrderProductFormSet(request.POST)
        if form.is_valid() and formset.is_valid():
            order = form.save()
            for form in formset:
                if form.cleaned_data and not form.cleaned_data.get('DELETE'):
                    order_product = form.save(commit=False)
                    order_product.order = order
                    order_product.save()

            return redirect('order_list')
        else:
            print(form.errors)
            print(formset.errors)
    else:
        form = OrderForm()
        formset = OrderProductFormSet()
    return render(request, 'ecom/Admin/Ordering/order_form.html', {'form': form, 'formset': formset})

@user_passes_test(is_admin, login_url='login')
@transaction.atomic
def update_order(request, pk):
    order = get_object_or_404(Orders, pk=pk)
    if request.method == 'POST':
        # Log the POST data for debugging
        logger.info(f"Update order POST data for order {pk}:")
        for key, value in request.POST.items():
            logger.info(f"  {key}: {value}")

        form = OrderForm(request.POST, instance=order)
        formset = OrderProductFormSet(request.POST, instance=order)

        # Check form validity separately to better identify issues
        form_valid = form.is_valid()
        formset_valid = formset.is_valid()

        if form_valid and formset_valid:
            # Save the order first
            order = form.save()

            # Process the formset but don't save yet
            formset.save(commit=False)

            # Process deletions explicitly using Django's mechanisms
            for form in formset.forms:
                if form.cleaned_data.get('DELETE', False):
                    if form.instance.pk:  # Only delete if it exists in the database
                        form.instance.delete()
                else:
                    # Save the instance if it's not marked for deletion
                    instance = form.save(commit=False)
                    instance.order = order
                    instance.save()

            # Update order totals
            order.save()

            messages.success(request, 'Order updated successfully.')
            return redirect('order_list')
        else:
            # More detailed error messages
            if not form_valid:
                logger.error(f"Order form validation failed: {form.errors}")
                messages.error(request, 'Order form has errors:')
                for field, errors in form.errors.items():
                    for error in errors:
                        error_msg = f"{field}: {error}"
                        logger.error(error_msg)
                        messages.error(request, error_msg)

            if not formset_valid:
                logger.error(f"Product formset validation failed: {formset.errors}")
                messages.error(request, 'Product formset has errors:')
                # Check for non-form errors (formset level)
                if formset.non_form_errors():
                    logger.error(f"Formset non-form errors: {formset.non_form_errors()}")
                    for error in formset.non_form_errors():
                        error_msg = f"Formset error: {error}"
                        logger.error(error_msg)
                        messages.error(request, error_msg)

                # Check individual form errors
                for i, form_errors in enumerate(formset.errors):
                    if form_errors:
                        logger.error(f"Product #{i+1} errors: {form_errors}")
                        messages.error(request, f"Product #{i+1} errors:")
                        for field, errors in form_errors.items():
                            for error in errors:
                                error_msg = f"  - {field}: {error}"
                                logger.error(error_msg)
                                messages.error(request, error_msg)
    else:
        form = OrderForm(instance=order)
        formset = OrderProductFormSet(instance=order)

    return render(request, 'ecom/Admin/Ordering/order_form.html', {
        'form': form,
        'formset': formset,
        'order': order
    })


@user_passes_test(is_admin, login_url='login')
def order_detail(request, pk):
    """View order details"""
    order = get_object_or_404(Orders, pk=pk)
    products = order.orderproduct_set.all()

    return render(request, 'ecom/Admin/Ordering/order_detail.html', {
        'order': order,
        'products': products,
    })




@user_passes_test(is_admin, login_url='login')
@require_POST
def archive_order(request, pk):
    """Archive an order"""
    order = get_object_or_404(Orders, pk=pk)
    order.archive()
    messages.success(request, f'Order #{order.invoice} archived successfully!')
    return redirect('order_list')


@user_passes_test(is_admin, login_url='login')
@require_POST
def unarchive_order(request, pk):
    """Unarchive an order"""
    order = get_object_or_404(Orders, pk=pk)
    order.unarchive()
    messages.success(request, f'Order #{order.invoice} unarchived successfully!')
    return redirect('order_detail', pk=pk)


@user_passes_test(is_admin, login_url='login')
def get_product_info(request):
    """Get product information for the order form"""
    product_id = request.GET.get('product_id')
    if not product_id:
        return JsonResponse({'error': 'No product ID provided'}, status=400)

    try:
        product = Product.objects.get(pk=product_id)

        # Get available batches
        batches = Batch_exp.objects.filter(
            product=product,
            active=True,
            availability__gt=0  # Only get batches with available stock
        ).order_by('expiry')

        batch_data = []
        for batch in batches:
            batch_data.append({
                'id': batch.id,
                'batch': batch.batch,
                'expiry': batch.expiry.strftime('%Y-%m-%d') if batch.expiry else 'N/A',
                'availability': batch.availability,
                'price': float(batch.price),
                'pack': batch.pack.item if batch.pack else 'N/A',
            })

        # Get available packs (quantities) with their availability
        packs = []
        for pack in product.quantities.all():
            # Calculate total available quantity for this pack across all batches
            total_available = Batch_exp.objects.filter(
                product=product,
                active=True,
                availability__gt=0
            ).aggregate(total=Sum('availability'))['total'] or 0

            packs.append({
                'id': pack.id,
                'name': pack.item,
                'available_quantity': total_available
            })

        # Get GST information
        gst_rate = product.GST

        return JsonResponse({
            'success': True,
            'product_name': str(product),
            'availability': product.availability,
            'batches': batch_data,
            'packs': packs,
            'gst_rate': gst_rate,
        })
    except Product.DoesNotExist:
        return JsonResponse({'error': 'Product not found'}, status=404)
    except Exception as e:
        logger.error(f"Error in get_product_info: {str(e)}")
        return JsonResponse({'error': 'Internal server error'}, status=500)


@user_passes_test(is_admin, login_url='login')
def get_price_info(request):
    """Get pricing information for customer and product combination"""
    customer_id = request.GET.get('customer_id')
    product_id = request.GET.get('product_id')
    pack_id = request.GET.get('pack_id')

    if not all([customer_id, product_id]):
        return JsonResponse({'error': 'Missing required parameters'}, status=400)

    try:
        # Get price list entry for this customer, product, and pack
        filters = {
            'customer_id': customer_id,
            'product_id': product_id,
        }

        if pack_id:
            filters['quantity_id'] = pack_id

        price_list = PriceList.objects.filter(**filters).first()

        if price_list:
            return JsonResponse({
                'success': True,
                'price': float(price_list.price),
                'discount': float(price_list.discount),
            })
        else:
            # If no custom price found, return product's default GST and price
            product = Product.objects.get(pk=product_id)
            return JsonResponse({
                'success': True,
                'price': float(product.price) if hasattr(product, 'price') else None,
                'discount': 0,
            })
    except Product.DoesNotExist:
        return JsonResponse({'error': 'Product not found'}, status=404)
    except Exception as e:
        logger.error(f"Error in get_price_info: {str(e)}")
        return JsonResponse({'error': 'Internal server error'}, status=500)

#---------------------------------------------------------------------------------
#------------------------ Products RELATED VIEWS START ---------------------------
#---------------------------------------------------------------------------------
@user_passes_test(is_admin, login_url='login')
def admin_products_view(request):
    if request.method == 'POST':
        query = request.POST.get('q')
        products=models.Product.objects.all().filter(item__icontains=query)
        return render(request, 'ecom/Admin/admin_products.html', {'products':products})

    products=models.Product.objects.all()

    paginator = Paginator(products, 50)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)

    return render(request,'ecom/Admin/admin_products.html',{'products':page_obj})

@user_passes_test(is_admin, login_url='login')
def admin_brand_view(request):

    if request.method == 'POST':
        query = request.POST.get('q')
        brands=models.Brand.objects.all().filter(name__icontains=query)
        return render(request, 'ecom/Admin/brand.html', {'brands':brands})

    brands=models.Brand.objects.all()
    return render(request,'ecom/Admin/brand.html',{'brands':brands})


@user_passes_test(is_admin, login_url='login')
def admin_pack_view(request):
    if request.method == 'POST':
        query = request.POST.get('q')
        packs=models.Quantity.objects.all().filter(item__icontains=query)
        return render(request, 'ecom/Admin/pack.html', {'quantities':packs})

    packs=models.Quantity.objects.all()
    return render(request,'ecom/Admin/pack.html',{'quantities':packs})


# admin add product by clicking on floating button
@user_passes_test(is_admin, login_url='login')
def admin_add_product_view(request):
    productForm=ProductForm()
    if request.method=='POST':
        productForm=ProductForm(request.POST, request.FILES)
        if productForm.is_valid():
            productForm.save()
        else:
            return HttpResponse(productForm.errors)

        return HttpResponseRedirect('admin-products')
    return render(request,'ecom/Admin/admin_add_products.html',{'productForm':productForm})


@user_passes_test(is_admin, login_url='login')
def admin_delete_product_view(request, pk):
    product = get_object_or_404(models.Product, id=pk)

    # Check for associations
    order_products = models.OrderProduct.objects.filter(product=product)
    batch_exps = models.Batch_exp.objects.filter(product=product)

    if request.method == 'POST' and not order_products.exists() and not batch_exps.exists():
        # No associations, safe to delete
        product.delete()
        messages.success(request, f'Product "{product.item}" has been deleted successfully.')
        return redirect('admin-products')

    # Render template with associations
    context = {
        'product': product,
        'order_products': order_products,
        'batch_exps': batch_exps,
        'can_delete': not (order_products.exists() or batch_exps.exists())
    }

    return render(request, 'ecom/Admin/admin_delete_product.html', context)


@user_passes_test(is_admin, login_url='login')
def admin_add_brand_view(request):
    if request.method == 'POST':
        name = request.POST.get('data')
        new_brand = models.Brand(name=name)
        new_brand.save()
        return HttpResponseRedirect('admin-brand')
    return redirect('admin-brand')

@user_passes_test(is_admin, login_url='login')
def admin_add_quantity_view(request):
    if request.method == 'POST':
        item = request.POST.get('data')

        new_quantity = models.Quantity(item=item)
        new_quantity.save()

        # Check if this is an AJAX request
        if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
            return HttpResponse(status=200)

        return HttpResponseRedirect('admin-pack')
    return redirect('admin-pack')




@user_passes_test(is_admin, login_url='login')
def update_product_view(request,pk):
    product=models.Product.objects.get(id=pk)
    productForm=ProductForm(instance=product)
    if request.method=='POST':
        productForm=ProductForm(request.POST,request.FILES,instance=product)
        if productForm.is_valid():
            productForm.save()
            return redirect('admin-products')
        else:
            return HttpResponse(productForm.errors)
    return render(request,'ecom/Admin/admin_update_product.html',{'productForm':productForm})


from django import forms as frm
#---------------------------------------------------------------------------------
#------------------------ Batch List RELATED VIEWS START ---------------------------
#---------------------------------------------------------------------------------
@user_passes_test(is_admin, login_url='login')
def admin_batch_view(request):
    orders=models.Purchase.objects.all()

    form = OrderFilterForm(request.POST or None)
    form.fields['customer'] = frm.ModelChoiceField(queryset=models.Supplier.objects.all(), required=False)


    if request.method == 'POST' and form.is_valid():
        from_date = form.cleaned_data.get('from_date')
        to_date = form.cleaned_data.get('to_date')
        customer = form.cleaned_data.get('customer')
        invoice = form.cleaned_data.get('invoice')

        if from_date:
            orders = orders.filter(date__gte=from_date)
        if to_date:
            orders = orders.filter(date__lte=to_date)
        if customer:
            orders = orders.filter(supplier=customer)
        if invoice:
            orders = orders.filter(invoice__contains=invoice) or orders.filter(inv__contains=invoice)


        return render(request,'ecom/Admin/admin_batch.html',{'purchase':orders, 'filterForm':form})

    orders = sorted(orders, key=lambda x: x.inv, reverse=True)

    paginator = Paginator(orders, 100)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)


    return render(request,'ecom/Admin/admin_batch.html',{'purchase':page_obj, 'filterForm':form})




    purchase=models.Purchase.objects.all()
    purchase = sorted(purchase, key=lambda x: x.inv, reverse=True)

    paginator = Paginator(purchase, 50)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)

    return render(request,'ecom/Admin/admin_batch.html',{'purchase':page_obj})





@user_passes_test(is_admin, login_url='login')
@transaction.atomic
def purchase_create(request):
    if request.method == 'POST':
        form = PurchaseForm(request.POST)
        formset = BatchExpFormSet(request.POST)
        if form.is_valid() and formset.is_valid():
            purchase = form.save()
            for form in formset:
                if form.cleaned_data and not form.cleaned_data.get('DELETE'):
                    purchase_product = form.save(commit=False)
                    purchase_product.purchase = purchase
                    purchase_product.save()

            return redirect('purchase_list')
        else:
            print(form.errors)
            print(formset.errors)
    else:
        form = PurchaseForm()
        formset = BatchExpFormSet()
    return render(request, 'ecom/Admin/Purchase/purchase_form.html', {'form': form, 'formset': formset})




@user_passes_test(is_admin, login_url='login')
@transaction.atomic
def purchase_update(request, pk):
    purchase = get_object_or_404(Purchase, pk=pk)
    if request.method == 'POST':
        form = PurchaseForm(request.POST, instance=purchase)
        formset = BatchExpFormSet(request.POST, instance=purchase)

        if form.is_valid() and formset.is_valid():
            # Save the order first
            purchase = form.save()

            # Process the formset but don't save yet
            formset.save(commit=False)

            # Process deletions explicitly using Django's mechanisms
            for form in formset.forms:
                if form.cleaned_data.get('DELETE', False):
                    if form.instance.pk:  # Only delete if it exists in the database
                        form.instance.delete()
                else:
                    # Save the instance if it's not marked for deletion
                    instance = form.save(commit=False)
                    instance.purchase = purchase
                    instance.save()

            # Update order totals
            purchase.save()

            messages.success(request, 'Purchase updated successfully.')
            return redirect('purchase_list')
        else:
            messages.error(request, 'Please correct the errors below.')
    else:
        form = PurchaseForm(instance=purchase)
        formset = BatchExpFormSet(instance=purchase)

    return render(request, 'ecom/Admin/Purchase/purchase_form.html', {
        'form': form,
        'formset': formset,
        'purchase': purchase
    })



@user_passes_test(is_admin, login_url='login')
def get_product_info(request):
    """AJAX endpoint to get product information"""
    if request.method == 'GET' and 'product_id' in request.GET:
        product_id = request.GET.get('product_id')
        try:
            product = models.Product.objects.get(pk=product_id)

            # Get available batches with availability > 0
            batches = models.Batch_exp.objects.filter(
                product=product,
                active=True,
                availability__gt=0  # Only get batches with available stock
            ).order_by('expiry')

            batch_data = []
            for batch in batches:
                batch_data.append({
                    'id': batch.id,
                    'batch': batch.batch,
                    'expiry': batch.expiry.strftime('%Y-%m-%d') if batch.expiry else 'N/A',
                    'availability': batch.availability,
                    'pack': batch.pack.item if batch.pack else 'N/A',
                })

            return JsonResponse({
                'gst_rate': product.GST,
                'hsn': product.HSN,
                'quantities': list(product.quantities.values('id', 'item')),
                'batches': batch_data
            })
        except models.Product.DoesNotExist:
            return JsonResponse({'error': 'Product not found'}, status=404)
        except Exception as e:
            logger.error(f"Error in get_product_info: {str(e)}")
            return JsonResponse({'error': 'Internal server error'}, status=500)

    return JsonResponse({'error': 'Invalid request'}, status=400)


@user_passes_test(is_admin, login_url='login')
def purchase_list(request):
    """View to display a list of all purchases"""
    # Start with all non-archived purchases
    purchases = models.Purchase.objects.filter(is_archived=False)

    # Get all suppliers for the dropdown
    suppliers = models.Supplier.objects.all().order_by('name')

    # Get financial years for the dropdown (could be calculated from purchase dates)
    # This is a placeholder - you may need to adjust based on your actual data
    current_year = datetime.now().year
    financial_years = range(current_year - 5, current_year + 1)

    # Handle search parameter
    search_query = request.GET.get('search', '')
    if search_query:
        # Search in both invoice and inv fields
        purchases = purchases.filter(
            Q(invoice__icontains=search_query) |
            Q(inv__icontains=search_query) |
            Q(supplier__name__icontains=search_query)
        )

    # Handle supplier filter
    supplier_id = request.GET.get('supplier')
    if supplier_id:
        purchases = purchases.filter(supplier_id=supplier_id)

    # Handle financial year filter
    financial_year = request.GET.get('financial_year')
    if financial_year:
        # Implement financial year filtering logic here
        # This is a placeholder - adjust based on your financial year definition
        try:
            year = int(financial_year)
            purchases = purchases.filter(date__year=year)
        except (ValueError, TypeError):
            pass

    # Handle status filter
    status = request.GET.get('status')
    if status == 'paid':
        purchases = purchases.filter(paid=True)
    elif status == 'unpaid':
        purchases = purchases.filter(paid=False)

    # Apply final ordering
    purchases = purchases.order_by('-inv')

    # Pagination
    paginator = Paginator(purchases, 50)  # Show 50 purchases per page
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)

    context = {
        'purchases': page_obj,
        'suppliers': suppliers,
        'financial_years': financial_years,
        'selected_year': financial_year,
        'selected_supplier': supplier_id,
        'status': status,
        'search_query': search_query
    }

    return render(request, 'ecom/Admin/Purchase/purchase_list.html', context)

@user_passes_test(is_admin, login_url='login')
def purchase_detail(request, pk):
    """View to display the details of a specific purchase"""
    purchase = get_object_or_404(models.Purchase, pk=pk)
    batch_items = models.Batch_exp.objects.filter(purchase=purchase)
    return render(request, 'ecom/Admin/Purchase/purchase_detail.html', {
        'purchase': purchase,
        'batch_items': batch_items
    })



@user_passes_test(is_admin, login_url='login')
def purchase_toggle_paid(request, pk):
    """View to toggle the paid status of a purchase"""
    purchase = get_object_or_404(models.Purchase, pk=pk)
    purchase.paid = not purchase.paid
    purchase.save()

    messages.success(request, f"Payment status updated to {'Paid' if purchase.paid else 'Unpaid'}")
    return redirect('purchase_detail', pk=purchase.pk)


# validate_file_size function has been moved to models.py


@user_passes_test(is_admin, login_url='login')
def bill_purchase_view(request, pk):
    """View to handle invoice PDF upload for a purchase"""
    purchase = get_object_or_404(models.Purchase, id=pk)

    # Ensure media directory exists
    upload_dir = os.path.join(settings.MEDIA_ROOT, 'Purchase_invoices')
    os.makedirs(upload_dir, exist_ok=True)

    if request.method == 'POST':
        if request.FILES.get('invoice_pdf'):
            file = request.FILES.get('invoice_pdf')

            # Validate file type
            if not file.name.lower().endswith('.pdf'):
                messages.error(request, "Only PDF files are allowed.")
                return render(request, 'ecom/Admin/upload_invoice.html', {'purchase': purchase})

            # Validate file size
            try:
                validate_file_size(file)
            except ValidationError as e:
                messages.error(request, f"File size error: {str(e)}")
                return render(request, 'ecom/Admin/upload_invoice.html', {'purchase': purchase})

            try:
                # Delete old file if it exists - use Django's built-in delete method
                if purchase.invoice_pdf:
                    try:
                        # Get the file path
                        old_file_path = os.path.join(settings.MEDIA_ROOT, str(purchase.invoice_pdf))
                        # Delete the file from storage
                        if os.path.isfile(old_file_path):
                            os.remove(old_file_path)
                    except Exception as e:
                        # Log the error but continue with the upload
                        print(f"Error deleting old file: {str(e)}")

                # Save new file
                purchase.invoice_pdf = file
                purchase.save()

                # Verify file was saved
                new_file_path = os.path.join(settings.MEDIA_ROOT, str(purchase.invoice_pdf))
                if os.path.isfile(new_file_path):
                    messages.success(request, "Invoice PDF uploaded successfully.")
                else:
                    messages.warning(request, "File upload processed but could not verify file was saved correctly.")

                return redirect('purchase_detail', pk=purchase.pk)

            except Exception as e:
                messages.error(request, f"Error uploading file: {str(e)}")
                return render(request, 'ecom/Admin/upload_invoice.html', {'purchase': purchase})
        else:
            messages.error(request, "No file was selected.")
            return render(request, 'ecom/Admin/upload_invoice.html', {'purchase': purchase})
    else:
        # GET request - show the upload form
        context = {'purchase': purchase}
        if purchase.invoice_pdf:
            context['inv'] = purchase.invoice_pdf

            # Check if file actually exists
            file_path = os.path.join(settings.MEDIA_ROOT, str(purchase.invoice_pdf))
            if not os.path.isfile(file_path):
                messages.warning(request, "The invoice file is registered but cannot be found on the server.")

        return render(request, 'ecom/Admin/upload_invoice.html', context)





#---------------------------------------------------------------------------------
#------------------------ Price List RELATED VIEWS START ---------------------------
#---------------------------------------------------------------------------------
@user_passes_test(is_admin, login_url='login')
def admin_price_view(request):
    # Start with all price list entries
    price_list_queryset = models.PriceList.objects.all()

    # Get filter parameters from both GET and POST
    customer_id = request.POST.get('customer') or request.GET.get('customer')
    product_id = request.POST.get('product') or request.GET.get('product')

    # Apply filters
    filters_applied = False

    if customer_id:
        try:
            customer = models.Customer.objects.get(id=customer_id)
            price_list_queryset = price_list_queryset.filter(customer=customer)
            filters_applied = True
        except models.Customer.DoesNotExist:
            customer_id = None

    if product_id:
        try:
            product = models.Product.objects.get(id=product_id)
            price_list_queryset = price_list_queryset.filter(product=product)
            filters_applied = True
        except models.Product.DoesNotExist:
            product_id = None

    # Order by customer name, then product name for consistent display
    price_list_queryset = price_list_queryset.select_related('customer__user', 'product', 'quantity').order_by(
        'customer__user__first_name', 'product__item'
    )

    # Pagination
    paginator = Paginator(price_list_queryset, 50)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)

    # Get all customers and products for filter dropdowns
    customers = models.Customer.objects.select_related('user').order_by('user__first_name')
    products = models.Product.objects.order_by('item')

    context = {
        'products': page_obj,
        'customers': customers,
        'all_products': products,
        'selected_customer': customer_id,
        'selected_product': product_id,
        'filters_applied': filters_applied,
        'total_entries': price_list_queryset.count()
    }

    return render(request, 'ecom/Admin/admin_price.html', context)


@user_passes_test(is_admin, login_url='login')
def admin_add_price_view(request):
    from .forms import PriceList as PriceListForm
    priceForm = PriceListForm()
    if request.method=='POST':
        priceForm = PriceListForm(request.POST)
        if priceForm.is_valid():
            priceForm.save()
            return HttpResponseRedirect('admin-price')
        else:
            return HttpResponse(priceForm.errors)
    return render(request, 'ecom/Admin/admin_add_price.html', {'priceForm':priceForm})

@user_passes_test(is_admin, login_url='login')
def admin_update_price_view(request,pk):
    from .forms import PriceList as PriceListForm
    price_list = models.PriceList.objects.get(id=pk)
    priceForm = PriceListForm(instance=price_list)
    if request.method=='POST':
        priceForm = PriceListForm(request.POST, instance=price_list)
        if priceForm.is_valid():
            priceForm.save()
            return redirect('admin-price')
        else:
            return HttpResponse(priceForm.errors)
    return render(request, 'ecom/Admin/admin_add_price.html', {'priceForm': priceForm})






#---------------------------------------------------------------------------------
#------------------------ Inventory RELATED VIEWS START ---------------------------
#---------------------------------------------------------------------------------
@user_passes_test(is_admin, login_url='login')
def admin_inventory_view(request):
    if request.method == 'POST':
        query = request.POST.get('q')
        # Include archive filter in inventory search
        show_archived = request.POST.get('show_archived', False)
        batches = models.Batch_exp.objects.all()

        if query:
            batches = batches.filter(product__item__icontains=query)
        if not show_archived:
            batches = batches.filter(is_archived=False)

        return render(request,'ecom/Admin/admin_inventory.html', {'batches': batches})

    sort_by = request.GET.get('sort_by', 'expiry')
    sort_order = request.GET.get('order', 'asc')
    show_archived = request.GET.get('show_archived', False)

    # Build base queryset
    batches = models.Batch_exp.objects.all()

    # Filter archived records unless explicitly requested
    if not show_archived:
        batches = batches.filter(is_archived=False)

    # Handle sorting
    if sort_by in ['id', '-id']:
        if '-' in sort_by:
            batches = sorted(batches, key=lambda x: x.purchase.inv, reverse=True)
        else:
            batches = sorted(batches, key=lambda x: x.purchase.inv)
    else:
        sort_prefix = '-' if sort_order == 'desc' else ''
        batches = batches.order_by(f"{sort_prefix}{sort_by}")

    paginator = Paginator(batches, 50)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)

    return render(request,'ecom/Admin/admin_inventory.html', {
        'batches': page_obj,
        'sort_order': sort_order,
        'show_archived': show_archived
    })



@user_passes_test(is_admin, login_url='login')
def admin_update_batch_view(request, pk):
     batch = models.Batch_exp.objects.get(id=pk)
     form = BatchForm(instance=batch)
     if request.method == 'POST':
         form = BatchForm(request.POST, instance=batch)
         if form.is_valid():
             #prod = models.Product.objects.get(item=purchaseForm.cleaned_data['product'])
             #prod.availability = (prod.availability - avail) + purchaseForm.cleaned_data['availability']
             form.save()
             #prod.save()
             return redirect('admin-inventory')
         else:
             return HttpResponse(form.errors)
     return render(request, 'ecom/Admin/admin_update_batch.html', {'form': form})

#---------------------------------------------------------------------------------
#------------------------ Ledger RELATED VIEWS START ---------------------------
#---------------------------------------------------------------------------------

from ledger import create_ledger

@user_passes_test(is_admin, login_url='login')
def admin_ledger_view(request):
    from decimal import Decimal
    from django.db.models import Q
    from datetime import datetime
    from django.contrib import messages

    # Get filter parameters from both GET and POST
    from_date = request.POST.get('from_date') or request.GET.get('from_date')
    to_date = request.POST.get('to_date') or request.GET.get('to_date')
    party_name = request.POST.get('party_name') or request.GET.get('party_name')
    search_query = request.POST.get('search_query') or request.GET.get('search_query')
    show_archived = bool(request.POST.get('show_archived') or request.GET.get('show_archived'))

    # Clean up empty strings
    if from_date == '':
        from_date = None
    if to_date == '':
        to_date = None
    if party_name == '':
        party_name = None
    if search_query == 'None':
        search_query = None

    # Build query parameters
    query_params = {}
    if not show_archived:
        query_params['is_archived'] = False

    # Handle date filtering with proper validation
    if from_date:
        try:
            # Ensure date is in proper format
            if isinstance(from_date, str):
                datetime.strptime(from_date, '%Y-%m-%d')
            query_params['date__gte'] = from_date
        except ValueError:
            messages.error(request, 'Invalid from date format. Please use YYYY-MM-DD format.')
            from_date = None

    if to_date:
        try:
            # Ensure date is in proper format
            if isinstance(to_date, str):
                datetime.strptime(to_date, '%Y-%m-%d')
            query_params['date__lte'] = to_date
        except ValueError:
            messages.error(request, 'Invalid to date format. Please use YYYY-MM-DD format.')
            to_date = None

    if party_name:
        query_params['party'] = party_name

    # Start with base queryset - add debugging
    try:
        ledgers = models.Ledger.objects.filter(**query_params)
        logger.info(f"Base ledger query with params {query_params} returned {ledgers.count()} results")
    except Exception as e:
        logger.error(f"Error in base ledger query: {e}")
        messages.error(request, f'Database error: {str(e)}')
        ledgers = models.Ledger.objects.none()

    # Add search functionality with better performance
    if search_query:
        try:
            search_q = Q(remark__icontains=search_query)

            # Search in party names
            search_q |= Q(party__icontains=search_query)

            # Search in transaction details more efficiently
            try:
                # Try to convert search query to integer for invoice searches
                search_int = int(search_query)
                search_q |= Q(transaction_object_id__in=models.Orders.objects.filter(invoice=search_int).values_list('id', flat=True))
                search_q |= Q(transaction_object_id__in=models.Purchase.objects.filter(inv=search_int).values_list('id', flat=True))
                search_q |= Q(transaction_object_id__in=models.Payment.objects.filter(invoice=search_int).values_list('id', flat=True))
            except ValueError:
                # If not a number, search in string fields
                search_q |= Q(transaction_object_id__in=models.Purchase.objects.filter(invoice__icontains=search_query).values_list('id', flat=True))
                search_q |= Q(transaction_object_id__in=models.Payment.objects.filter(bank__icontains=search_query).values_list('id', flat=True))

            ledgers = ledgers.filter(search_q)
            logger.info(f"Search query '{search_query}' returned {ledgers.count()} results")
        except Exception as e:
            logger.error(f"Error in search query: {e}")
            messages.error(request, f'Search error: {str(e)}')

    # Order by date (most recent first)
    ledgers = ledgers.order_by('-date', '-id')

    # Handle print functionality
    if request.method == 'POST' and 'print' in request.POST:
        # Check if we have any ledger entries to print
        if not ledgers.exists():
            messages.error(request, 'No ledger entries found to print. Please adjust your filters.')
            return redirect('admin-ledger')

        # Determine party information
        party_obj = None
        party_display_name = "All Parties"
        address = ""
        gst = ""

        if party_name:
            try:
                party_obj = models.Customer.objects.get(user__first_name=party_name)
                party_display_name = str(party_obj.user.first_name)
                address = str(getattr(party_obj, 'address', ''))
                gst = str(getattr(party_obj, 'GSTIN', ''))
            except models.Customer.DoesNotExist:
                try:
                    party_obj = models.Supplier.objects.get(name=party_name)
                    party_display_name = str(party_obj.name)
                    address = str(getattr(party_obj, 'address', ''))
                    gst = str(getattr(party_obj, 'GSTIN', ''))
                except models.Supplier.DoesNotExist:
                    party_display_name = party_name

        try:
            pdf = create_ledger(from_date, to_date, party_display_name, address, gst, ledgers)
            return pdf
        except Exception as e:
            messages.error(request, f'Error generating PDF: {str(e)}')
            return redirect('admin-ledger')

    # Calculate opening balance
    opening_balance = Decimal('0.00')
    if party_name and from_date:
        # Get all transactions before from_date for the party
        opening_ledgers = models.Ledger.objects.filter(
            party=party_name,
            date__lt=from_date,
            is_archived=False
        ).order_by('date')

        for entry in opening_ledgers:
            if entry.type == 'sales' or (hasattr(entry.transaction, 'type') and entry.transaction.type in ['debit', 'DEBIT']):
                opening_balance += entry.transaction.amount
            else:
                opening_balance -= entry.transaction.amount

    # Calculate closing balance (opening balance + current period transactions)
    closing_balance = opening_balance
    for entry in ledgers:
        if entry.type == 'sales' or (hasattr(entry.transaction, 'type') and entry.transaction.type in ['debit', 'DEBIT']):
            closing_balance += entry.transaction.amount
        else:
            closing_balance -= entry.transaction.amount

    # Pagination
    paginator = Paginator(ledgers, 25)  # Increased from 20 to 25
    page_number = request.GET.get('page', 1)
    ledgers_page = paginator.get_page(page_number)

    # Get party choices with better organization
    party_choices = []

    # Add customers
    customers = models.Customer.objects.all().order_by('user__first_name')
    for customer in customers:
        if customer.user.first_name:  # Only add if name exists
            party_choices.append(str(customer.user.first_name))

    # Add suppliers
    suppliers = models.Supplier.objects.all().order_by('name')
    for supplier in suppliers:
        if supplier.name:  # Only add if name exists
            party_choices.append(str(supplier.name))

    # Remove duplicates and sort
    party_choices = sorted(list(set(party_choices)))

    # Debug information
    debug_info = {
        'total_ledgers_before_pagination': ledgers.count(),
        'total_ledgers_in_db': models.Ledger.objects.count(),
        'total_non_archived_ledgers': models.Ledger.objects.filter(is_archived=False).count(),
        'from_date': from_date,
        'to_date': to_date,
        'party_name': party_name,
        'search_query': search_query,
        'show_archived': show_archived,
        'query_params': query_params,
        'sample_ledger_parties': list(models.Ledger.objects.values_list('party', flat=True).distinct()[:10]),
        'sample_ledger_types': list(models.Ledger.objects.values_list('type', flat=True).distinct()),
    }

    context = {
        'ledgers': ledgers_page,
        'party_choices': party_choices,
        'from_date': from_date,
        'to_date': to_date,
        'party_name': party_name,
        'search_query': search_query,
        'show_archived': show_archived,
        'opening_balance': opening_balance,
        'closing_balance': closing_balance,
        'debug_info': debug_info,
    }

    return render(request, 'ecom/Admin/ledger.html', context)


@user_passes_test(is_admin, login_url='login')
def admin_financials_view(request):
    # Get all payments, ordered by most recent first
    payments = models.Payment.objects.filter(is_archived=False).order_by('-date', '-id')

    # Get customer receivables (debit)
    customers = models.Customer.objects.all().select_related('user')
    customer_receivables = [
        {
            'name': customer.user.first_name,
            'amount': customer.debit,
            'type': 'Receivable',
            'id': customer.id
        }
        for customer in customers if customer.debit > 0
    ]

    # Get supplier outstandings (credit)
    suppliers = models.Supplier.objects.all()
    supplier_outstandings = [
        {
            'name': supplier.name,
            'amount': supplier.credit,
            'type': 'Payable',
            'id': supplier.id
        }
        for supplier in suppliers if supplier.credit > 0
    ]

    # Combine and sort by amount (highest first)
    financial_summary = sorted(
        customer_receivables + supplier_outstandings,
        key=lambda x: x['amount'],
        reverse=True
    )

    # Calculate totals
    total_receivables = sum(item['amount'] for item in customer_receivables)
    total_payables = sum(item['amount'] for item in supplier_outstandings)
    net_position = total_receivables - total_payables

    # Paginate payments
    paginator = Paginator(payments, 10)
    page_number = request.GET.get('page')
    paginated_payments = paginator.get_page(page_number)

    # Create payment form with today's date
    payment_form = PaymentForm()
    payment_form.fields['date'].initial = timezone.now().date()

    context = {
        'payments': paginated_payments,
        'financial_summary': financial_summary,
        'total_receivables': total_receivables,
        'total_payables': total_payables,
        'net_position': net_position,
        'payment_form': payment_form,
        'today': timezone.now().date()
    }

    return render(request, 'ecom/Admin/financials.html', context)



@user_passes_test(is_admin, login_url='login')
def admin_payment_view(request):

    if request.method == 'POST':
        form = PaymentForm(request.POST)
        if form.is_valid():
            payment = form.save(commit=False)

            # Add remark if provided
            if request.POST.get('remark'):
                payment.remark = request.POST.get('remark')

            # Update customer/supplier balances
            type = form.cleaned_data['type']
            try:
                if type == 'CREDIT':
                    # Find the customer and update their debit
                    party = models.Customer.objects.get(user__first_name=form.cleaned_data['party'])
                    party.debit = party.debit - form.cleaned_data['amount']
                    party.save()
                    logger.info(f"Updated customer {party.user.first_name} debit to {party.debit}")
                else:
                    # Find the supplier and update their credit
                    party = models.Supplier.objects.get(name=form.cleaned_data['party'])
                    party.credit = party.credit - form.cleaned_data['amount']
                    party.save()
                    logger.info(f"Updated supplier {party.name} credit to {party.credit}")

                # Save the payment
                payment.save()
                messages.success(request, f"Payment of ₹{payment.amount} for {payment.party} recorded successfully.")

                # Redirect based on where the form was submitted from
                redirect_url = request.POST.get('next', 'admin-financials')
                return redirect(redirect_url)

            except (models.Customer.DoesNotExist, models.Supplier.DoesNotExist) as e:
                messages.error(request, f"Error: Party '{form.cleaned_data['party']}' not found.")
                logger.error(f"Party not found error: {str(e)}")
                return redirect('admin-financials')
        else:
            messages.error(request, "Please correct the errors in the form.")
            logger.error(f"Payment form validation failed: {form.errors}")

            # If the form was submitted from the financials page, redirect back there
            if request.META.get('HTTP_REFERER', '').endswith('admin-financials/'):
                return redirect('admin-financials')

            # Otherwise show the standalone payment form
            return render(request, 'ecom/Admin/add_payment.html', {'form': form})

    # Get all payments initially
    payments = Payment.objects.filter(is_archived=False).order_by('-date', '-id')
    
    # Get filter parameters from request
    party_filter = request.GET.get('party', '').strip()
    type_filter = request.GET.get('type', '')
    method_filter = request.GET.get('method', '')
    date_from = request.GET.get('date_from', '')
    date_to = request.GET.get('date_to', '')
    search_query = request.GET.get('search', '').strip()
    
    # Apply filters
    if party_filter:
        payments = payments.filter(party__icontains=party_filter)
    
    if type_filter:
        payments = payments.filter(type=type_filter)
    
    if method_filter:
        payments = payments.filter(method=method_filter)
    
    if date_from:
        try:
            date_from_obj = datetime.strptime(date_from, '%Y-%m-%d').date()
            payments = payments.filter(date__gte=date_from_obj)
        except ValueError:
            pass
    
    if date_to:
        try:
            date_to_obj = datetime.strptime(date_to, '%Y-%m-%d').date()
            payments = payments.filter(date__lte=date_to_obj)
        except ValueError:
            pass
    
    # Search functionality
    if search_query:
        payments = payments.filter(
            Q(party__icontains=search_query) |
            Q(transaction_id__icontains=search_query) |
            Q(bank__icontains=search_query) |
            Q(remark__icontains=search_query)
        )
    
    # Get unique parties for dropdown
    unique_parties = Payment.objects.filter(is_archived=False).values_list('party', flat=True).distinct().order_by('party')
    
    # Calculate totals for current filtered results
    total_credit = sum(p.amount or 0 for p in payments if p.type == 'CREDIT')
    total_debit = sum(p.amount or 0 for p in payments if p.type == 'DEBIT')
    net_amount = total_credit - total_debit
    
    # Pagination
    paginator = Paginator(payments, 25)  # Show 25 payments per page
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)
    
    context = {
        'page_obj': page_obj,
        'payments': page_obj,
        'unique_parties': unique_parties,
        'type_choices': Payment.TYPE_CHOICES,
        'method_choices': Payment.METHOD_CHOICES,
        # Preserve filter values for form
        'current_filters': {
            'party': party_filter,
            'type': type_filter,
            'method': method_filter,
            'date_from': date_from,
            'date_to': date_to,
            'search': search_query,
        },
        # Summary statistics
        'summary': {
            'total_records': paginator.count,
            'total_credit': total_credit,
            'total_debit': total_debit,
            'net_amount': net_amount,
        }
    }
    
    return render(request, 'ecom/Admin/admin_payments.html', context)



# @user_passes_test(is_admin, login_url='login')
# def admin_payment_view(request):
#     if request.method == 'POST':
#         form = PaymentForm(request.POST)
#         if form.is_valid():
#             payment = form.save(commit=False)

#             # Add remark if provided
#             if request.POST.get('remark'):
#                 payment.remark = request.POST.get('remark')

#             # Update customer/supplier balances
#             type = form.cleaned_data['type']
#             try:
#                 if type == 'CREDIT':
#                     # Find the customer and update their debit
#                     party = models.Customer.objects.get(user__first_name=form.cleaned_data['party'])
#                     party.debit = party.debit - form.cleaned_data['amount']
#                     party.save()
#                     logger.info(f"Updated customer {party.user.first_name} debit to {party.debit}")
#                 else:
#                     # Find the supplier and update their credit
#                     party = models.Supplier.objects.get(name=form.cleaned_data['party'])
#                     party.credit = party.credit - form.cleaned_data['amount']
#                     party.save()
#                     logger.info(f"Updated supplier {party.name} credit to {party.credit}")

#                 # Save the payment
#                 payment.save()
#                 messages.success(request, f"Payment of ₹{payment.amount} for {payment.party} recorded successfully.")

#                 # Redirect based on where the form was submitted from
#                 redirect_url = request.POST.get('next', 'admin-financials')
#                 return redirect(redirect_url)

#             except (models.Customer.DoesNotExist, models.Supplier.DoesNotExist) as e:
#                 messages.error(request, f"Error: Party '{form.cleaned_data['party']}' not found.")
#                 logger.error(f"Party not found error: {str(e)}")
#                 return redirect('admin-financials')
#         else:
#             messages.error(request, "Please correct the errors in the form.")
#             logger.error(f"Payment form validation failed: {form.errors}")

#             # If the form was submitted from the financials page, redirect back there
#             if request.META.get('HTTP_REFERER', '').endswith('admin-financials/'):
#                 return redirect('admin-financials')

#             # Otherwise show the standalone payment form
#             return render(request, 'ecom/Admin/add_payment.html', {'form': form})
#     else:
#         form = PaymentForm()
#         # Set initial date to today
#         form.fields['date'].initial = timezone.now().date()

#     return render(request, 'ecom/Admin/add_payment.html', {'form': form})


@user_passes_test(is_admin, login_url='login')
def update_payment_view(request, pk):
    payment = get_object_or_404(models.Payment, id=pk)

    if request.method == 'POST':
        form = PaymentForm(request.POST, instance=payment)
        if form.is_valid():
            updated_payment = form.save(commit=False)

            # Add remark if provided
            if request.POST.get('remark'):
                updated_payment.remark = request.POST.get('remark')

            # Set financial year based on date
            updated_payment.financial_year = updated_payment.get_financial_year_for_date(updated_payment.date)

            updated_payment.save()
            messages.success(request, f"Payment for {updated_payment.party} updated successfully.")
            return redirect('admin-financials')
        else:
            messages.error(request, "Please correct the errors in the form.")
    else:
        form = PaymentForm(instance=payment)
        # Pre-populate remark field
        form.initial['remark'] = payment.remark

    return render(request, 'ecom/Admin/update_payment.html', {'form': form, 'payment': payment})



#---------------------------------------------------------------------------------
#------------------------ Notes RELATED VIEWS START ---------------------------
#---------------------------------------------------------------------------------
@user_passes_test(is_admin, login_url='login')
def admin_notes_view(request):
    query_params = {}
    if request.method == 'POST':
        if request.POST.get('party_name'):
            query_params['party'] = request.POST.get('party_name')
        if request.POST.get('from_date'):
            query_params['date__gte'] = request.POST.get('from_date')
        if request.POST.get('to_date'):
            query_params['date__lte'] = request.POST.get('to_date')

        notes = models.Admin_Notes.objects.filter(**query_params).order_by('-date')
    else:
        notes = models.Admin_Notes.objects.all().order_by('-date')

    party_choices = list(itertools.chain((str(c.user.first_name) for c in models.Customer.objects.all()), (str(s.name) for s in models.Supplier.objects.all())))
    return render(request, 'ecom/Admin/notes.html' , {'party_choices': party_choices , 'notes': notes})


@user_passes_test(is_admin, login_url='login')
def create_note_view(request):
    if request.method == 'POST':
        form = Admin_NoteForm(request.POST)
        if form.is_valid():
            form.save()
            return redirect('admin-notes')
        else:
            return HttpResponse(form.errors)
    else:
        form = Admin_NoteForm()

    return render(request,'ecom/Admin/create_update_notes.html' , {'form': form})

@user_passes_test(is_admin, login_url='login')
def update_note_view(request, pk):
    note = models.Admin_Notes.objects.get(id=pk)
    form = Admin_NoteForm(instance=note)

    if request.method == 'POST':
        form = Admin_NoteForm(request.POST, instance=note)
        if form.is_valid():
            form.save()
            return redirect('admin-notes')

    return render(request,'ecom/Admin/create_update_notes.html',{'form': form})

from notes import create_note
@user_passes_test(is_admin, login_url='login')
def print_note_view(request, pk):
    note = models.Admin_Notes.objects.get(id=pk)
    pdf = create_note(note)
    return pdf


#---------------------------------------------------------------------------------
#------------------------ GSTR RELATED VIEWS START ---------------------------
#---------------------------------------------------------------------------------

def generate_hsn_report(request, orders, month, year, b2c, print_report, show_archived):
    """Generate HSN-wise report for GSTR1"""
    from collections import defaultdict
    import openpyxl
    import io

    # HSN aggregation data structure
    hsn_data = defaultdict(lambda: {
        'items': set(),  # Use set to avoid duplicate items
        'uqc': '',  # Unit of Quantity Code
        'total_quantity': 0,
        'total_value_with_tax': 0,
        'total_taxable_value': 0,
        'igst': 0,
        'cgst': 0,
        'sgst': 0,
        'cess': 0  # Total Cess (currently 0 as not implemented)
    })

    for order in orders:
        # Apply B2B/B2C filter
        if b2c:
            if order.customer.GSTIN:
                continue
        else:
            if not order.customer.GSTIN:
                continue

        # Get order products
        order_products = models.OrderProduct.objects.filter(order=order)
        if not show_archived:
            order_products = order_products.filter(is_archived=False)

        for order_product in order_products:
            hsn_code = order_product.product.HSN
            if hsn_code:
                # Add item name to the set
                hsn_data[hsn_code]['items'].add(order_product.product.item)

                # Set UQC (Unit of Quantity Code) - use pack if available
                if order_product.pack and not hsn_data[hsn_code]['uqc']:
                    hsn_data[hsn_code]['uqc'] = order_product.pack.item
                elif not hsn_data[hsn_code]['uqc']:
                    hsn_data[hsn_code]['uqc'] = 'NOS'  # Default unit

                # Aggregate quantities and amounts
                hsn_data[hsn_code]['total_quantity'] += order_product.quantity or 0
                hsn_data[hsn_code]['total_value_with_tax'] += order_product.amount_tax or 0
                hsn_data[hsn_code]['total_taxable_value'] += order_product.amount or 0

                # Calculate tax breakdown based on place of supply
                gst_amount = order_product.gst or 0
                if order.customer.place_of_supply and order.customer.place_of_supply != 'Karnataka':
                    # IGST for inter-state
                    hsn_data[hsn_code]['igst'] += gst_amount
                else:
                    # CGST + SGST for intra-state
                    hsn_data[hsn_code]['cgst'] += gst_amount / 2
                    hsn_data[hsn_code]['sgst'] += gst_amount / 2

    # Convert to list format for template rendering
    hsn_report = []
    for hsn_code, data in hsn_data.items():
        entry = [
            hsn_code,  # HSN
            ', '.join(sorted(data['items'])),  # Items (comma-separated)
            data['uqc'],  # UQC
            data['total_quantity'],  # Total Quantity
            round(data['total_value_with_tax'], 2),  # Total Value (with tax)
            round(data['total_taxable_value'], 2),  # Total Taxable Value (without tax)
            round(data['igst'], 2),  # Integrated Tax
            round(data['cgst'], 2),  # Central Tax
            round(data['sgst'], 2),  # State/UT Tax
            round(data['cess'], 2)   # Total Cess
        ]
        hsn_report.append(entry)

    # Sort by HSN code
    hsn_report.sort(key=lambda x: x[0])

    if print_report:
        # Generate Excel file
        wb = openpyxl.Workbook()
        sheet = wb.active
        sheet.title = "HSN Report"

        # Add headers
        headers = ['HSN', 'Item', 'UQC', 'Total Quantity', 'Total Value (with tax)',
                  'Total Taxable Value (without tax)', 'Integrated Tax', 'Central Tax',
                  'State/UT Tax', 'Total Cess']
        sheet.append(headers)

        # Add data rows
        for row in hsn_report:
            sheet.append(row)

        # Create response
        output = io.BytesIO()
        wb.save(output)
        output.seek(0)

        response = HttpResponse(output.getvalue(),
                              content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet')
        response['Content-Disposition'] = f'attachment; filename=hsn_report_{month}_{year}.xlsx'
        return response

    return render(request, 'ecom/Admin/gstr1.html', {
        'hsn_report': hsn_report,
        'month': month,
        'year': year,
        'is_hsn_report': True
    })



@user_passes_test(is_admin, login_url='login')
def gstr1_view(request):
    if request.method == 'POST':
        month = request.POST.get('month')
        year = request.POST.get('year')
        b2c = request.POST.get('toggle')
        print = request.POST.get('print')
        generate_hsn = request.POST.get('generate_hsn')
        show_archived = request.POST.get('show_archived', False)

        # Base queryset
        orders = models.Orders.objects.filter(date__month=month, date__year=year)
        if not show_archived:
            orders = orders.filter(is_archived=False)

        # Check if HSN report is requested
        if generate_hsn:
            return generate_hsn_report(request, orders, month, year, b2c, print, show_archived)

        gstr = []
        orders = sorted(orders, key=lambda x: x.invoice)

        taxable_total = 0
        total_tax = 0
        amount_with_tax = 0

        aggregated_data = defaultdict(lambda: {
            'quantity': 0,
            'total_amount': 0,
            'total_gst': 0,
            'amount_with_tax': 0
        })

        for order in orders:
            if b2c:
                if order.customer.GSTIN:
                    continue
            else:
                if not order.customer.GSTIN:
                    continue

            # Get only non-archived products unless explicitly requested
            order_products = models.OrderProduct.objects.filter(order=order)
            if not show_archived:
                order_products = order_products.filter(is_archived=False)

            for order_product in order_products:
                # Use get_invoice() method to get the formatted invoice string
                formatted_invoice = order.get_invoice()
                gst_key = (order.date, formatted_invoice, order.customer, order.customer.GSTIN, order_product.gst_rate)
                aggregated_data[gst_key]['quantity'] += order_product.quantity
                aggregated_data[gst_key]['total_amount'] += order_product.amount
                aggregated_data[gst_key]['total_gst'] += order_product.gst
                aggregated_data[gst_key]['amount_with_tax'] += order_product.amount_tax

        # Generate the report
        gstr = []
        for key, values in aggregated_data.items():
            entry = []
            entry.append('Diatree Sales')
            entry.append(str(key[0]))  # order date
            entry.append(str(key[1]))  # invoice
            entry.append(str(key[2]))  # customer
            entry.append(str(key[3]))  # GSTIN
            entry.append('Karnataka')  # place of supply
            entry.append(values['quantity'])  # total quantity

            # total amount
            entry.append(values['total_amount'])

            # appending IGST
            entry.append('0')
            entry.append('0')

            # GST rates and amounts
            entry.append(str(key[4] / 2))  # CGST Rate
            entry.append(str(values['total_gst'] / 2))  # CGST Amount
            entry.append(str(key[4] / 2))  # SGST Rate
            entry.append(str(values['total_gst'] / 2))  # SGST Amount

            # amount with tax
            entry.append(values['amount_with_tax'])

            gstr.append(entry)
            taxable_total += values['total_amount']
            total_tax += values['total_gst']
            amount_with_tax += values['amount_with_tax']

        # Now gstr contains the aggregated entries ready for output

        gstr.append(['','','','Total','','Karnataka','',taxable_total,'0','0','0', total_tax/2,'0', total_tax/2,amount_with_tax])

        if print:
            wb = openpyxl.Workbook()
            sheet = wb.active

            sheet.append(['Voucher Type','Invoice Date', 'Invoice no.', 'Party Name', 'GSTIN/UIN', 'Place of Supply', 'Total Quantity', 'Total Taxable amount', 'IGST Rate', 'IGST Amount', 'CGST Rate', 'CGST Amount', 'SGST Rate', 'SGST Amount', 'Total Invoice Value'])

            for row in gstr:
                sheet.append(row)

            output = io.BytesIO()
            wb.save(output)

            # Move to the beginning of the stream
            output.seek(0)

            # Set the HTTP response headers
            response = HttpResponse(output.getvalue(), content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet')
            response['Content-Disposition'] = f'attachment; filename=gstr_{month}_{year}.xlsx'

            return response

        return render(request, 'ecom/Admin/gstr1.html', {'orders': gstr, 'month': month, 'year': year})
    #create custom tags for taxes
    start_year = 2024
    end_year = datetime.now().year
    years = range(start_year, end_year + 1)


    return render(request, 'ecom/Admin/gstr1.html', {'years': years})


@user_passes_test(is_admin, login_url='login')
def gstr2_view(request):
    if request.method == 'POST':
        try:
            month = request.POST.get('month')
            year = request.POST.get('year')
            prin = request.POST.get('print') == 'true'
            show_archived = request.POST.get('show_archived') == 'true'

            # Base queryset
            purchases = models.Purchase.objects.filter(date__month=month, date__year=year)
            if not show_archived:
                purchases = purchases.filter(is_archived=False)

            purchases = sorted(purchases, key=lambda x: x.inv)

            taxable_total = 0
            total_tax = 0
            igst_tax = 0
            amount_with_tax = 0

            aggregated_data = defaultdict(lambda: {
                'quantity': 0,
                'total_amount': 0,
                'total_igst': 0,
                'total_gst': 0,
                'amount_with_tax': 0
            })

            for purchase in purchases:
                # Get only non-archived batch entries unless explicitly requested
                batch_products = models.Batch_exp.objects.filter(purchase=purchase)
                if not show_archived:
                    batch_products = batch_products.filter(is_archived=False)

                for batch_product in batch_products:
                    # Use str() to ensure inv is treated as a string
                    inv_str = str(purchase.inv) if purchase.inv else "N/A"

                    # Get place of supply with fallback to 'Karnataka' if not set
                    place_of_supply = purchase.supplier.place_of_supply if purchase.supplier.place_of_supply else 'Karnataka'

                    # Ensure GST rate is not None
                    gst_rate = batch_product.gst_rate if batch_product.gst_rate is not None else 0

                    gst_key = (purchase.date, inv_str, purchase.supplier, purchase.supplier.GSTIN or "N/A", gst_rate)

                    # Ensure quantity is not None
                    quantity = batch_product.quantity if batch_product.quantity is not None else 0
                    aggregated_data[gst_key]['quantity'] += quantity

                    # Ensure amount is not None
                    amount = batch_product.amount if batch_product.amount is not None else 0
                    aggregated_data[gst_key]['total_amount'] += amount

                    # Ensure gst is not None
                    gst = batch_product.gst if batch_product.gst is not None else 0

                    if place_of_supply == 'Karnataka':
                        aggregated_data[gst_key]['total_gst'] += gst
                    else:
                        aggregated_data[gst_key]['total_igst'] += gst

                    # Ensure amount_tax is not None
                    amount_with_tax_value = batch_product.amount_tax if batch_product.amount_tax is not None else 0
                    aggregated_data[gst_key]['amount_with_tax'] += amount_with_tax_value

            # Generate the report
            gstr = []
            for key, values in aggregated_data.items():
                entry = []
                entry.append('Diatree Purchase')
                entry.append(str(key[0]))  # order date
                entry.append(str(key[1]))  # invoice
                entry.append(str(key[2]))  # supplier
                entry.append(str(key[3]))  # GSTIN

                # Get place of supply with fallback
                place_of_supply = getattr(key[2], 'place_of_supply', None) or 'Karnataka'
                entry.append(place_of_supply)

                entry.append(values['quantity'])  # total quantity
                entry.append(values['total_amount'])  # total amount

                if place_of_supply == 'Karnataka':
                    # appending IGST
                    entry.append('0')
                    entry.append('0')

                    # GST rates and amounts
                    entry.append(str(key[4] / 2))  # CGST Rate
                    entry.append(str(values['total_gst'] / 2))  # CGST Amount
                    entry.append(str(key[4] / 2))  # SGST Rate
                    entry.append(str(values['total_gst'] / 2))  # SGST Amount
                else:
                    # appending IGST
                    entry.append(str(key[4]))
                    entry.append(str(values['total_igst']))

                    # GST rates and amounts
                    entry.append('0')  # CGST Rate
                    entry.append('0')  # CGST Amount
                    entry.append('0')  # SGST Rate
                    entry.append('0')  # SGST Amount

                # amount with tax
                entry.append(values['amount_with_tax'])

                gstr.append(entry)
                taxable_total += values['total_amount']
                if place_of_supply == 'Karnataka':
                    total_tax += values['total_gst']
                else:
                    igst_tax += values['total_igst']
                amount_with_tax += values['amount_with_tax']

            # Add totals row
            gstr.append(['','','','Total','','Karnataka','',taxable_total,'0',igst_tax,'0', total_tax/2,'0', total_tax/2,amount_with_tax])

            if prin:
                wb = openpyxl.Workbook()
                sheet = wb.active

                sheet.append(['Voucher Type','Invoice Date', 'Invoice no.', 'Party Name', 'GSTIN/UIN', 'Place of Supply', 'Total Quantity', 'Total Taxable amount', 'IGST Rate', 'IGST Amount', 'CGST Rate', 'CGST Amount', 'SGST Rate', 'SGST Amount', 'Total Invoice Value'])

                for row in gstr:
                    sheet.append(row)

                output = io.BytesIO()
                wb.save(output)

                # Move to the beginning of the stream
                output.seek(0)

                # Set the HTTP response headers
                response = HttpResponse(output.getvalue(), content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet')
                response['Content-Disposition'] = f'attachment; filename=gstr2_{month}_{year}.xlsx'

                return response

            # Add success message
            messages.success(request, f"GSTR-2 report generated successfully for {month}/{year}")

            return render(request, 'ecom/Admin/gstr2.html', {
                'orders': gstr,
                'month': month,
                'year': year,
                'show_archived': show_archived
            })

        except Exception as e:
            # Log the error
            logger.error(f"Error in gstr2_view: {str(e)}")
            messages.error(request, f"An error occurred while generating the report: {str(e)}")

            # Return to the form with the error message
            start_year = 2024
            end_year = datetime.now().year
            years = range(start_year, end_year + 1)
            return render(request, 'ecom/Admin/gstr2.html', {'years': years, 'error': str(e)})

    # Initial GET request
    start_year = 2024
    end_year = datetime.now().year
    years = range(start_year, end_year + 1)

    return render(request, 'ecom/Admin/gstr2.html', {'years': years})


from gstr3 import create_file
@user_passes_test(is_admin, login_url='login')
def gstr3_view(request):
    if request.method == 'POST':
        try:
            month = request.POST.get('month')
            year = request.POST.get('year')
            prin = request.POST.get('print') == 'true'
            show_archived = request.POST.get('show_archived') == 'true'

            # Get base querysets
            orders = models.Orders.objects.filter(date__month=month, date__year=year)
            purchases = models.Purchase.objects.filter(date__month=month, date__year=year)

            # Filter archived records unless explicitly requested
            if not show_archived:
                orders = orders.filter(is_archived=False)
                purchases = purchases.filter(is_archived=False)

            # Calculate totals from non-archived records
            total = orders.aggregate(Sum('total'))['total__sum'] or 0
            total_amount = orders.aggregate(Sum('amount'))['amount__sum'] or 0
            tax = total_amount - total

            igst = 0
            cgst = 0

            for purchase in purchases:
                # Get place of supply with fallback to 'Karnataka' if not set
                place_of_supply = purchase.supplier.place_of_supply if purchase.supplier.place_of_supply else 'Karnataka'

                # Ensure total and amount are not None
                purchase_total = purchase.total if purchase.total is not None else 0
                purchase_amount = purchase.amount if purchase.amount is not None else 0

                if place_of_supply != 'Karnataka':
                    igst += purchase_amount - purchase_total
                else:
                    cgst += purchase_amount - purchase_total

            if prin:
                pdf = create_file([
                    str(round(total, 2)),
                    str(round(tax/2, 2)),
                    str(round(igst, 2)),
                    str(round(cgst/2, 2)),
                    month,
                    year
                ])
                return pdf

            # Add success message
            messages.success(request, f"GSTR-3 report generated successfully for {month}/{year}")

            # Return with context for form persistence
            return render(request, 'ecom/Admin/gstr3.html', {
                'month': month,
                'year': year,
                'show_archived': show_archived
            })

        except Exception as e:
            # Log the error
            logger.error(f"Error in gstr3_view: {str(e)}")
            messages.error(request, f"An error occurred while generating the report: {str(e)}")

            # Return to the form with the error message
            start_year = 2024
            end_year = datetime.now().year
            years = range(start_year, end_year + 1)
            return render(request, 'ecom/Admin/gstr3.html', {'years': years, 'error': str(e)})

    # Initial GET request
    start_year = 2024
    end_year = datetime.now().year
    years = range(start_year, end_year + 1)

    return render(request, 'ecom/Admin/gstr3.html', {'years': years})



#---------------------------------------------------------------------------------
#------------------------ Settings RELATED VIEWS START ---------------------------
#---------------------------------------------------------------------------------
@user_passes_test(is_admin, login_url='login')
def admin_settings_view(request):
    return render(request, 'ecom/Admin/settings.html')


@user_passes_test(is_admin, login_url='login')
def admin_import_db_view(request):
    if request.method == 'POST' and request.FILES.get('file'):
        uploaded_file = request.FILES['file']
        db_path = 'db.sqlite3'

        file_extension = os.path.splitext(uploaded_file.name)[1]
        if file_extension != '.sqlite3':
            return HttpResponseBadRequest("Invalid file type. Please upload a SQLite3 database file.")

        with open(db_path, 'wb') as f:
            for chunk in uploaded_file.chunks():
                f.write(chunk)

        return redirect('/')

    return HttpResponseBadRequest("No file uploaded or invalid request.")



@user_passes_test(is_admin, login_url='login')
def admin_export_db_view(request):
    db_path = 'db.sqlite3'

    if not os.path.exists(db_path):
        return HttpResponse("Database file not found", status=404)

    with open(db_path, 'rb') as f:
        response = HttpResponse(f.read(), content_type='application/x-sqlite3')

    response['Content-Disposition'] = 'attachment; filename="%s"' % smart_str(os.path.basename(db_path))

    return response


@user_passes_test(is_admin, login_url='login')
def admin_export_model_view(request,model):
    response = HttpResponse(content_type='text/csv')
    response['Content-Disposition'] = f'attachment; filename="{model}_data.csv"'
    fields = []

    if model == 'Product':
        fields = ['Item', 'Brand', 'Availability', 'GST', 'HSN', 'Quantities']
        def data_from_obj(obj):
            quantities = ', '.join([quantity.item for quantity in obj.quantities.all()])
            data = [obj.item, obj.brand, obj.availability, obj.GST, obj.HSN, quantities]
            return data

    elif model == 'Customer':
        fields = ['Name', 'address', 'email', 'mobile', 'Price_List', 'GSTIN', 'debit', 'Place_of_Supply']

        def data_from_obj(obj):
            data = [str(obj.user.first_name), obj.address, obj.email, obj.mobile, obj.priceList, obj.GSTIN, obj.debit, obj.place_of_supply]
            return data

    elif model == 'Supplier':
        fields = ['Name', 'address', 'email', 'mobile', 'GSTIN', 'credit', 'Place_of_Supply']

        def data_from_obj(obj):
            data = [obj.name, obj.address, obj.email, obj.mobile, obj.GSTIN, obj.credit, obj.place_of_supply]
            return data

    elif model == 'PriceList':
        fields = ['Product', 'Quantity', 'Customer', 'Price', 'Per Unit Price', 'Discount', 'GST']

        def data_from_obj(obj):
            data = [obj.product.item, obj.quantity, obj.customer, obj.price, obj.per_unit_price, obj.discount, obj.gst]
            return data

    elif model == 'Orders':
        fields = ['Invoice', 'PO', 'Customer', 'Total', 'Amount', 'Date', 'Status']

        def data_from_obj(obj):
            data = [obj.invoice, obj.po, obj.customer, obj.total, obj.amount, obj.date, obj.status]
            return data

    elif model == 'Purchase':
        fields = ['Supplier', 'Invoice', 'Inv', 'Date', 'Total', 'Amount', 'Paid']

        def data_from_obj(obj):
            data = [obj.supplier, obj.invoice, obj.inv, obj.date, obj.total, obj.amount, obj.paid]
            return data

    elif model == 'Batch_exp':
        fields = ['Purchase', 'Product', 'Pack', 'Batch', 'Expiry', 'Quantity', 'Availability', 'Active', 'Price', 'Discount', 'Amount', 'GST Rate', 'GST', 'Net Amount']

        def data_from_obj(obj):
            data = [obj.purchase, obj.product.item, obj.pack, obj.batch, obj.expiry, obj.quantity, obj.availability, obj.active, obj.price, obj.discount, obj.amount, obj.gst_rate, obj.gst, obj.amount_tax]
            return data

    elif model == 'OrderProduct':
        fields = ['Order', 'Product', 'Quantity', 'Pack', 'Batches', 'Price', 'Discount', 'Amount', 'GST Rate', 'GST', 'Net Amount']

        def data_from_obj(obj):
            batch_info = obj.batch_exp.batch if obj.batch_exp else "No batch"
            data = [obj.order.invoice, obj.product.item, obj.quantity, obj.pack, batch_info, obj.price, obj.discount, obj.amount, obj.gst_rate, obj.gst, obj.amount_tax]
            return data

    elif model == 'Admin_Notes':
        fields = ['invoice', 'date', 'type', 'party', 'amount', 'gst', 'remark']

        def data_from_obj(obj):
            data = [obj.invoice, obj.date, obj.type, obj.party, obj.amount, obj.gst, obj.remark]
            return data


    elif model == 'Payment':
        fields = ['invoice', 'party', 'date', 'type', 'method', 'amount', 'transaction_id', 'bank', 'remark']

        def data_from_obj(obj):
            data = [obj.invoice, obj.date, obj.type, obj.method, obj.amount, obj.transaction_id, obj.bank, obj.remark]
            return data



    writer = csv.writer(response)
    writer.writerow(fields)
    model_class = getattr(models, model)
    queryset = model_class.objects.all()
    for obj in queryset:
        writer.writerow(data_from_obj(obj))

    return response


@user_passes_test(is_admin, login_url='login')
def admin_import_product_view(request):
    if request.method == 'POST' and request.FILES.get('file2'):
        uploaded_file = request.FILES['file2']
        if uploaded_file.name.endswith('.csv'):
            decoded_file = uploaded_file.read().decode('utf-8').splitlines()
            csv_data = csv.DictReader(decoded_file, delimiter=',')
            counter = 0
            for row in csv_data:
                counter += 1
                print(counter)
                name = row.get('Item Description', '')
                name = name.strip()
                brand = row.get('Brand', '')
                pack = row.get('Pack', '')
                gst_group = row.get('GST Group', '')
                hsncode = row.get('HSN Code', '')

                if name:
                    try:
                        product, created = models.Product.objects.get_or_create(item=name, defaults={'brand': models.Brand.objects.get_or_create(name=brand)[0]})
                    except Exception as e:
                        print(f"Error while creating product '{name}': {e}")
                    if created:
                        product.save()
                    if pack:
                        quantity, created = models.Quantity.objects.get_or_create(item=pack)
                        product.quantities.add(quantity)
                    if gst_group:
                        # Extract only the numeric value from GST Group (e.g., "GST 18%" -> "18")
                        product.GST = re.sub(r'[^\d.]', '', gst_group)
                    if hsncode:
                        product.HSN = hsncode
                    product.save()
            return redirect('/')

        else:
           return HttpResponseBadRequest("Invalid file type. Please upload a CSV file.")

    return HttpResponseBadRequest("No file uploaded or invalid request.")



@user_passes_test(is_admin, login_url='login')
def admin_export_product_view(request):
    response = HttpResponse(content_type='text/csv')
    response['Content-Disposition'] = 'attachment; filename="Product_data.csv"'

    writer = csv.writer(response)
    writer.writerow(['Item', 'Brand', 'Availability', 'GST', 'HSN', 'Quantities'])
    queryset = models.Product.objects.all()
    for obj in queryset:
        writer.writerow([obj.item, obj.brand, obj.availability, obj.GST, obj.HSN, obj.quantities])  # Replace field1, field2, etc., with your actual field names

    return response



@user_passes_test(is_admin, login_url='login')
def admin_import_customer_view(request):
    if request.method == 'POST' and request.FILES.get('file1'):
        uploaded_file = request.FILES['file1']

        if uploaded_file.name.endswith('.csv'):
            decoded_file = uploaded_file.read().decode('utf-8').splitlines()
            csv_data = csv.DictReader(decoded_file, delimiter=',')
            counter = 0
            for row in csv_data:
                counter += 1
                print(counter)
                name = row.get('Party Name', '')
                gst_no = row.get('GST No', '')
                address1 = row.get('Address1', '')
                address2 = row.get('Address2', '')
                address3 = row.get('Address3', '')
                pincode = row.get('Pincode', '')
                area = row.get('Area', '')
                city = row.get('City', '')
                state = row.get('State', '')
                phone_no = row.get('Phone No', '')
                mobile_no = row.get('Mobile No', '')
                email_id = row.get('E-Mail Id', '')
                variables = [address1, address2, address3, area, city, pincode, state]
                address = ' '.join(variable for variable in variables if variable)

                #note the customers group should be set from the admin panel, only
                #then will they actually become a customer
                if name:
                    user, _ = models.User.objects.get_or_create(
                        first_name=name, username='user-'+str(counter))
                    customer = models.Customer.objects.create(
                        user=user, GSTIN=gst_no, address=address, email=email_id)

                    if phone_no:
                        customer.mobile = phone_no[:20]  # Limit to 20 characters
                    elif mobile_no:
                        customer.mobile = mobile_no[:20]  # Limit to 20 characters

                    customer.save()
            return redirect('/')
        else:
           return HttpResponseBadRequest("Invalid file type. Please upload a text file.")


    return HttpResponseBadRequest("No file uploaded or invalid request.")




@user_passes_test(is_admin, login_url='login')
def admin_import_supplier_view(request):
    if request.method == 'POST' and request.FILES.get('file3'):
        uploaded_file = request.FILES['file3']

        if uploaded_file.name.endswith('.csv'):
            decoded_file = uploaded_file.read().decode('utf-8').splitlines()
            csv_data = csv.DictReader(decoded_file, delimiter=',')
            counter = 0
            for row in csv_data:
                counter += 1
                print(counter)
                name = row.get('Party Name', '')
                gst_no = row.get('GST No', '')
                address1 = row.get('Address1', '')
                address2 = row.get('Address2', '')
                address3 = row.get('Address3', '')
                pincode = row.get('Pincode', '')
                area = row.get('Area', '')
                city = row.get('City', '')
                state = row.get('State', '')
                phone_no = row.get('Phone No', '')
                mobile_no = row.get('Mobile No', '')
                email_id = row.get('E-Mail Id', '')
                variables = [address1, address2, address3, area, city, pincode, state]
                address = ' '.join(variable for variable in variables if variable)

                if name:
                    supplier,_ = models.Supplier.objects.get_or_create(name=name, GSTIN=gst_no, address=address, email=email_id)
                    supplier.save()

                    if phone_no:
                        supplier.mobile = phone_no[:20]  # Limit to 20 characters
                    elif mobile_no:
                        supplier.mobile = mobile_no[:20]  # Limit to 20 characters

                    supplier.save()
            return redirect('/')
        else:
           return HttpResponseBadRequest("Invalid file type. Please upload a text file.")


    return HttpResponseBadRequest("No file uploaded or invalid request.")


@user_passes_test(is_admin, login_url='login')
def admin_export_price_list_view(request):
    response = HttpResponse(content_type='text/csv')
    response['Content-Disposition'] = 'attachment; filename="price_list.csv"'
    writer = csv.writer(response)

    writer.writerow(['Product', 'Quantity', 'Customer', 'Price', 'Per Unit Price', 'Discount', 'GST'])
    for obj in models.PriceList.objects.all().values_list('product__item', 'quantity__item', 'cust__first_name', 'price', 'per_unit_price', 'discount', 'gst'):
        writer.writerow(obj)

    return response


@user_passes_test(is_admin, login_url='login')
def admin_import_price_list_view(request):
    if request.method == 'POST' and request.FILES.get('file4'):
        uploaded_file = request.FILES['file4']

        decoded_file = uploaded_file.read().decode('utf-8').splitlines()
        csv_reader = csv.reader(decoded_file)
        next(csv_reader)  # Skip header row

        # Iterate over each row in the CSV file
        for row in csv_reader:
            product = row[0]
            quantity = row[1]
            customer = row[2]
            price = row[3]
            per_unit_price = row[4]
            discount = row[5]

            try:
                item = models.Product.objects.get(item=product)
                quantity = models.Quantity.objects.get(item=quantity)
                customer = models.Customer.objects.get(user__first_name = customer)
            except:
                print(product + ' does not exist in the database')

            print(customer)
            price_list = models.PriceList.objects.create(
                product=item,
                quantity=quantity,
                customer=customer,
                price=price,
                per_unit_price=decimal.Decimal(per_unit_price) if per_unit_price else None,
                discount=decimal.Decimal(discount) if discount else None
            )
            price_list.save()

        return redirect('admin-price')

    return HttpResponseBadRequest("No file uploaded or invalid request.")

@user_passes_test(is_admin, login_url='login')
def admin_import_stock_view(request):
    if request.method == 'POST' and request.FILES.get('file5'):
        uploaded_file = request.FILES['file5']

        decoded_file = uploaded_file.read().decode('utf-8').splitlines()
        csv_reader = csv.reader(decoded_file)
        next(csv_reader)  # Skip header row

        # Iterate over each row in the CSV file
        for row in csv_reader:
            item = row[0]
            quantity = row[1]
            batch_no = row[3]
            try:
                exp_date = datetime.strptime(row[2], '%Y-%m-%d').date()
            except:
                exp_date = None

            if '::' in item:
                name, pack = item.split('::')
                pack = models.Quantity.objects.get_or_create(item=pack)[0]
                if '{' in name:
                    pattern = r'\{([^}]*)\}'
                    match = re.search(pattern, name)
                    brand_match = match.group(1)
                    try:
                        brand = models.Brand.objects.get(name=brand_match)
                    except:
                        print(item)

                    name = name.replace(f"{{{brand_match}}}", "")
                    name = name.strip()
                    try:
                        product = models.Product.objects.get(item=name, brand=brand)
                    except:
                        print(item)
                else:
                    try:
                        product = models.Product.objects.get(item=name)
                    except:
                        print(item)
                product.quantities.add(pack)
                product.save()
            elif len(item) <= 60:
                try:
                    product = models.Product.objects.get(item=name)
                except:
                    print(item)
                product.save()
            else:
                print(item)


            # Create a Batch_exp object
            batch_exp = models.Batch_exp.objects.create(
                purchase=None,
                product=product,
                batch=batch_no,
                pack=pack,
                expiry=exp_date,
                quantity=quantity.split('.')[0],
                availability=quantity.split('.')[0],
                active=True
            )


    return HttpResponseBadRequest("No file uploaded or invalid request.")


@user_passes_test(is_admin, login_url='login')
def admin_import_purchases_view(request):
    if request.method == 'POST' and request.FILES.get('file6'):
        uploaded_file = request.FILES['file6']

        decoded_file = uploaded_file.read().decode('utf-8').splitlines()
        csv_reader = csv.DictReader(decoded_file)

        for row in csv_reader:
            try:
                supplier = models.Supplier.objects.get(name=row.get('Party Name'))
            except:
                print(row.get('Party Name') + ' does not exist in the database')

            purchase_date = datetime.strptime(row.get('Date'), '%d-%m-%Y %H:%M:%S')
            purchase_date_only = purchase_date.date()
            purchase = models.Purchase.objects.create(
                    invoice=row.get('Ref.Doc No.'),
                    date=purchase_date_only,
                    inv=row.get('Number'),
                    supplier=supplier,
                    total=decimal.Decimal(row.get('Net Amount')) - decimal.Decimal(row.get('Tax Amount')),
                    amount=row.get('Net Amount'),
                )

            purchase.save()
        return redirect('admin-batch')


@user_passes_test(is_admin, login_url='login')
def admin_import_sales_view(request):
    if request.method == 'POST' and request.FILES.get('sales_file'):
        try:
            import pandas as pd
            from datetime import datetime
            from django.db import transaction
            from decimal import Decimal

            # Read the Excel file with explicit engine specification
            file = request.FILES['sales_file']
            if file.name.endswith('.xlsx'):
                df = pd.read_excel(file, engine='openpyxl')
            else:
                df = pd.read_excel(file, engine='xlrd')

            # Validate required columns
            required_columns = ['Date', 'Number', 'Party Name', 'Disc. Amount', 'Tax Amount', 'Net Amount']
            missing_columns = [col for col in required_columns if col not in df.columns]

            if missing_columns:
                messages.error(request, f"Missing required columns: {', '.join(missing_columns)}")
                return render(request, 'ecom/Admin/import_sales.html')

            # Process data within a transaction
            with transaction.atomic():
                orders_created = 0
                errors = []

                for index, row in df.iterrows():
                    try:
                        # Get or create customer
                        party_name = row['Party Name']
                        try:
                            # Try to find customer by name
                            customer = models.Customer.objects.filter(
                                user__first_name__iexact=party_name
                            ).first()

                            if not customer:
                                # Create a new user and customer with dummy data
                                from django.contrib.auth.models import User, Group
                                import uuid

                                # Generate a unique username based on the party name
                                username = f"auto_{party_name.lower().replace(' ', '_')}_{uuid.uuid4().hex[:8]}"

                                # Create user with a random password
                                user = User.objects.create_user(
                                    username=username,
                                    password=uuid.uuid4().hex,
                                    first_name=party_name
                                )

                                # Create customer
                                customer = models.Customer.objects.create(
                                    user=user,
                                    address="Auto-created during import",
                                    mobile="",
                                    email="",
                                    place_of_supply="Karnataka"
                                )

                                # Add to customer group
                                customer_group, _ = Group.objects.get_or_create(name='CUSTOMER')
                                user.groups.add(customer_group)

                                messages.info(request, f"Created new customer: {party_name} (row {index+2})")

                        except Exception as e:
                            errors.append(f"Error finding/creating customer {party_name}: {str(e)} (row {index+2})")
                            continue

                        # Parse date
                        try:
                            if isinstance(row['Date'], str):
                                # Try different date formats with time components
                                try:
                                    # Format with time: dd-mm-yyyy HH:MM:SS
                                    date = datetime.strptime(row['Date'], '%d-%m-%Y %H:%M:%S').date()
                                except ValueError:
                                    try:
                                        # Format with time: yyyy-mm-dd HH:MM:SS
                                        date = datetime.strptime(row['Date'], '%Y-%m-%d %H:%M:%S').date()
                                    except ValueError:
                                        try:
                                            # Format with time: dd/mm/yyyy HH:MM:SS
                                            date = datetime.strptime(row['Date'], '%d/%m/%Y %H:%M:%S').date()
                                        except ValueError:
                                            try:
                                                # Format without time: dd-mm-yyyy
                                                date = datetime.strptime(row['Date'], '%d-%m-%Y').date()
                                            except ValueError:
                                                try:
                                                    # Format without time: yyyy-mm-dd
                                                    date = datetime.strptime(row['Date'], '%Y-%m-%d').date()
                                                except ValueError:
                                                    # Format without time: dd/mm/yyyy
                                                    date = datetime.strptime(row['Date'], '%d/%m/%Y').date()
                            else:
                                # If it's already a datetime object, extract just the date part
                                date = row['Date'].date() if hasattr(row['Date'], 'date') else row['Date']
                        except Exception as e:
                            errors.append(f"Invalid date format in row {index+2}: {str(e)}")
                            continue

                        # Get invoice number - extract the last part after the last '/'
                        try:
                            # For format like "DI/25-26/3", extract just the '3'
                            number_str = str(row['Number'])
                            if '/' in number_str:
                                # Split by '/' and get the last part
                                invoice_number = int(number_str.split('/')[-1])
                            else:
                                # If no '/', try to convert the whole string to int
                                invoice_number = int(number_str)
                        except (ValueError, TypeError):
                            errors.append(f"Invalid invoice number in row {index+2}: {row['Number']} - Expected format like 'DI/25-26/3' or a numeric value")
                            continue

                        # Check if order with this invoice already exists
                        if models.Orders.objects.filter(invoice=invoice_number).exists():
                            errors.append(f"Order with invoice {invoice_number} already exists (row {index+2})")
                            continue

                        # Parse amounts
                        try:
                            disc_amount = Decimal(str(row['Disc. Amount'] or 0))
                            tax_amount = Decimal(str(row['Tax Amount'] or 0))
                            net_amount = Decimal(str(row['Net Amount'] or 0))
                            total_amount = net_amount - tax_amount  # Total without tax
                        except (ValueError, InvalidOperation, TypeError) as e:
                            errors.append(f"Invalid amount format in row {index+2}: {str(e)}")
                            continue

                        # Create order
                        order = models.Orders.objects.create(
                            invoice=invoice_number,
                            customer=customer,
                            date=date,
                            total=total_amount,
                            amount=net_amount,
                            status=models.Orders.StatusChoices.PENDING
                        )

                        orders_created += 1

                    except Exception as e:
                        errors.append(f"Error processing row {index+2}: {str(e)}")

                # Report results
                if orders_created > 0:
                    messages.success(request, f"Successfully imported {orders_created} orders.")

                if errors:
                    # Limit the number of errors shown to avoid overwhelming the user
                    max_errors_to_show = 10
                    error_message = "<br>".join(errors[:max_errors_to_show])
                    if len(errors) > max_errors_to_show:
                        error_message += f"<br>...and {len(errors) - max_errors_to_show} more errors."
                    messages.warning(request, f"Encountered {len(errors)} errors during import: <br>{error_message}", extra_tags='safe')

                return redirect('admin-view-booking')

        except Exception as e:
            messages.error(request, f"Error importing sales data: {str(e)}")
            return render(request, 'ecom/Admin/import_sales.html')

    return render(request, 'ecom/Admin/import_sales.html')

@user_passes_test(is_admin, login_url='login')
def admin_import_sales_objs_view(request):
    if request.method == 'POST' and request.FILES.get('file8'):
        uploaded_file = request.FILES['file8']

        try:
            decoded_file = uploaded_file.read().decode('utf-8').splitlines()
            csv_reader = csv.DictReader(decoded_file)

            success_count = 0
            error_count = 0
            errors = []

            with transaction.atomic():
                for row_num, row in enumerate(csv_reader, start=2):  # Start at 2 to account for header row
                    try:
                        item_description = row.get('Item Description', '').strip()
                        pack = row.get('Pack', '')
                        batch_number = row.get('Batch Number', '')

                        try:
                            expiry_date = datetime.strptime(row.get('Expiry Date', ''), '%Y-%m-%d').date()
                        except (ValueError, TypeError):
                            expiry_date = None

                        gst_group = row.get('GST Group', '0')
                        # Extract only the numeric value from GST Group (e.g., "GST 18%" -> "18")
                        gst_rate = decimal.Decimal(re.sub(r'[^\d.]', '', gst_group) or '0')
                        hsn_code = row.get('HSN Code', '')

                        try:
                            trans_rate = decimal.Decimal(row.get('TransRate', '0'))
                        except (decimal.InvalidOperation, TypeError):
                            trans_rate = decimal.Decimal('0')

                        try:
                            trans_qty = int(row.get('TransQty', '0'))
                        except (ValueError, TypeError):
                            trans_qty = 0

                        invoice = row.get('Number', '')

                        if not item_description:
                            errors.append(f"Row {row_num}: Missing item description")
                            error_count += 1
                            continue

                        if not invoice:
                            errors.append(f"Row {row_num}: Missing invoice number")
                            error_count += 1
                            continue

                        # Try to get the product, create if not found
                        try:
                            product = models.Product.objects.get(item=item_description)
                        except models.Product.DoesNotExist:
                            # Create product with dummy data
                            brand, _ = models.Brand.objects.get_or_create(name="Auto-created")
                            product = models.Product.objects.create(
                                item=item_description,
                                brand=brand,
                                GST=gst_rate,
                                HSN=hsn_code or "0000",
                                availability=0
                            )
                            messages.info(request, f"Created new product: {item_description} (row {row_num})")

                        # Get or create the order
                        try:
                            order = models.Orders.objects.get(invoice=invoice)
                        except models.Orders.DoesNotExist:
                            errors.append(f"Row {row_num}: Order with invoice {invoice} not found")
                            error_count += 1
                            continue

                        # Get or create the batch
                        batch_obj = None
                        if batch_number:
                            try:
                                batch_obj = models.Batch_exp.objects.get(batch=batch_number)
                            except models.Batch_exp.DoesNotExist:
                                # We'll leave batch as None if not found
                                pass

                        # Get or create the pack
                        pack_obj, _ = models.Quantity.objects.get_or_create(item=pack)

                        # Add the pack to the product's quantities if not already there
                        if pack_obj not in product.quantities.all():
                            product.quantities.add(pack_obj)
                            product.save()

                        # Create the order product
                        orderproduct = models.OrderProduct.objects.create(
                            order=order,
                            product=product,
                            pack=pack_obj,
                            price=trans_rate,
                            quantity=trans_qty,
                            gst_rate=gst_rate,
                        )

                        if batch_obj:
                            orderproduct.batch_exp.add(batch_obj)

                        orderproduct.save()
                        success_count += 1

                    except Exception as e:
                        errors.append(f"Row {row_num}: {str(e)}")
                        error_count += 1

            # Report results
            if success_count > 0:
                messages.success(request, f"Successfully imported {success_count} order products.")

            if errors:
                # Limit the number of errors shown to avoid overwhelming the user
                max_errors_to_show = 10
                error_message = "<br>".join(errors[:max_errors_to_show])
                if len(errors) > max_errors_to_show:
                    error_message += f"<br>...and {len(errors) - max_errors_to_show} more errors."
                messages.warning(request, f"Encountered {error_count} errors during import: <br>{error_message}", extra_tags='safe')

            return redirect('admin-view-booking')

        except Exception as e:
            messages.error(request, f"Error importing sales data: {str(e)}")

    return render(request, 'ecom/Admin/import_sales.html')



@user_passes_test(is_admin, login_url='login')
def admin_import_purchases_objs_view(request):
    if request.method == 'POST' and request.FILES.get('file9'):
        uploaded_file = request.FILES['file9']

        try:
            decoded_file = uploaded_file.read().decode('utf-8').splitlines()
            csv_reader = csv.DictReader(decoded_file)

            success_count = 0
            error_count = 0
            errors = []

            with transaction.atomic():
                for row_num, row in enumerate(csv_reader, start=2):  # Start at 2 to account for header row
                    try:
                        item_description = row.get('Item Description', '').strip()
                        pack = row.get('Pack', '')
                        batch_number = row.get('Batch Number', '')

                        try:
                            expiry_date = datetime.strptime(row.get('Expiry Date', ''), '%Y-%m-%d').date()
                        except (ValueError, TypeError):
                            expiry_date = None

                        gst_group = row.get('GST Group', '0')
                        # Extract only the numeric value from GST Group (e.g., "GST 18%" -> "18")
                        gst = decimal.Decimal(re.sub(r'[^\d.]', '', gst_group) or '0')
                        hsn_code = row.get('HSN Code', '')

                        try:
                            trans_qty = int(row.get('TransQty', '0'))
                        except (ValueError, TypeError):
                            trans_qty = 0

                        try:
                            trans_rate = decimal.Decimal(row.get('TransRate', '0'))
                        except (decimal.InvalidOperation, TypeError):
                            trans_rate = decimal.Decimal('0')

                        inv = row.get('Number', '')

                        if not item_description:
                            errors.append(f"Row {row_num}: Missing item description")
                            error_count += 1
                            continue

                        if not inv:
                            errors.append(f"Row {row_num}: Missing invoice number")
                            error_count += 1
                            continue

                        # Get the purchase
                        try:
                            purchase = models.Purchase.objects.get(inv=inv)
                        except models.Purchase.DoesNotExist:
                            errors.append(f"Row {row_num}: Purchase with invoice {inv} not found")
                            error_count += 1
                            continue

                        # Get or create the pack
                        pack_obj, _ = models.Quantity.objects.get_or_create(item=pack)

                        # Try to get the product, create if not found
                        try:
                            product = models.Product.objects.get(item=item_description)

                            # Log variations in GST and HSN but don't stop the import
                            if product.GST != gst:
                                messages.info(request, f"Row {row_num}: GST variation for product '{item_description}' - Existing: {product.GST}, Imported: {gst}")

                            if product.HSN != hsn_code and hsn_code:
                                messages.info(request, f"Row {row_num}: HSN variation for product '{item_description}' - Existing: {product.HSN}, Imported: {hsn_code}")

                        except models.Product.DoesNotExist:
                            # Create product with dummy data
                            brand, _ = models.Brand.objects.get_or_create(name="Auto-created")
                            product = models.Product.objects.create(
                                item=item_description,
                                brand=brand,
                                GST=gst,
                                HSN=hsn_code or "0000",
                                availability=0
                            )
                            messages.info(request, f"Created new product: {item_description} (row {row_num})")

                        # Add the pack to the product's quantities if not already there
                        if pack_obj not in product.quantities.all():
                            product.quantities.add(pack_obj)
                            product.save()

                        # Create or update the batch
                        batch, created = models.Batch_exp.objects.get_or_create(
                            batch=batch_number,
                            purchase=purchase,
                            product=product,
                            defaults={
                                'expiry': expiry_date,
                                'pack': pack_obj,
                                'quantity': trans_qty,
                                'availability': trans_qty,
                                'price': trans_rate,
                                'gst_rate': gst,
                                'active': True
                            }
                        )

                        # If the batch already existed, update its fields
                        if not created:
                            batch.expiry = expiry_date
                            batch.pack = pack_obj
                            batch.quantity = trans_qty
                            batch.availability = trans_qty
                            batch.price = trans_rate
                            batch.gst_rate = gst
                            batch.active = True
                            batch.save()

                        # Update product availability
                        product.availability = models.Batch_exp.objects.filter(
                            product=product, active=True
                        ).aggregate(total=Sum('availability'))['total'] or 0
                        product.save()

                        success_count += 1

                    except Exception as e:
                        errors.append(f"Row {row_num}: {str(e)}")
                        error_count += 1

            # Report results
            if success_count > 0:
                messages.success(request, f"Successfully imported {success_count} purchase items.")

            if errors:
                # Limit the number of errors shown to avoid overwhelming the user
                max_errors_to_show = 10
                error_message = "<br>".join(errors[:max_errors_to_show])
                if len(errors) > max_errors_to_show:
                    error_message += f"<br>...and {len(errors) - max_errors_to_show} more errors."
                messages.warning(request, f"Encountered {error_count} errors during import: <br>{error_message}", extra_tags='safe')

            return redirect('admin-batch')

        except Exception as e:
            messages.error(request, f"Error importing purchase data: {str(e)}")

    return render(request, 'ecom/Admin/import_purchase.html')

@user_passes_test(is_admin, login_url='login')
def admin_import_payments_view(request):
    if request.method == 'POST' and request.FILES.get('file10'):
        uploaded_file = request.FILES['file10']

        decoded_file = uploaded_file.read().decode('utf-8').splitlines()
        csv_reader = csv.DictReader(decoded_file)

        for row in csv_reader:
            voucher_type = row.get('Voucher Type')
            if voucher_type == 'Payment':
                voucher = row.get('Voucher No')
                bank, party = row.get('Particulars').split('To')
                bank = bank.strip('From ')
                party = party.strip()

                payment, _ = models.Payment.objects.get_or_create(
                    invoice=voucher,
                    party=party,
                    bank=bank,
                    method = 'NEFT',
                    date=datetime.strptime(row.get('Voucher Date'), '%d-%m-%Y').date(),
                    type='DEBIT',
                    amount=decimal.Decimal(row.get('Net Amount')),
                    remark=row.get('Remarks')
                )
                payment.save()
        return redirect('admin-ledger')


@user_passes_test(is_admin, login_url='login')
def admin_import_notes_view(request):
    if request.method == 'POST' and request.FILES.get('file11'):
        uploaded_file = request.FILES['file11']

        decoded_file = uploaded_file.read().decode('utf-8').splitlines()
        csv_reader = csv.DictReader(decoded_file)

        for row in csv_reader:
            models.Admin_Notes.objects.create(
                    invoice=row.get('Voucher No'),
                    date=datetime.strptime(row.get('Voucher Date'), '%d-%m-%Y').date(),
                    type=row.get('Voucher Type').upper(),
                    party=row.get('Party'),
                    gst=decimal.Decimal(row.get('gst')),
                    amount=decimal.Decimal(row.get('amount')) + decimal.Decimal(row.get('gst')),
                    remark=row.get('Remarks')
                )

        return redirect('admin-notes')

@user_passes_test(is_admin, login_url='login')
def admin_import_transactions_view(request):
    if request.method == 'POST' and request.FILES.get('file10'):
        uploaded_file = request.FILES['file10']

        decoded_file = uploaded_file.read().decode('utf-8').splitlines()
        csv_reader = csv.DictReader(decoded_file)

        for row in csv_reader:
            voucher_type = row.get('Voucher Type')
            voucher = row.get('Voucher No')
            sender, receiver = row.get('Particulars').split('To')
            sender = sender.strip('From ')
            receiver = receiver.strip()

            if voucher_type == 'DEBIT':
                payment, _ = models.Payment.objects.get_or_create(
                    invoice=voucher,
                    party=receiver,
                    bank=sender,
                    method = 'NEFT',
                    date=datetime.strptime(row.get('Voucher Date'), '%d-%m-%Y').date(),
                    type=voucher_type,
                    amount=decimal.Decimal(row.get('Net Amount')),
                    remark=row.get('Remarks')
                )

            else:
                payment, _ = models.Payment.objects.get_or_create(
                    invoice=voucher,
                    party=sender,
                    bank=receiver,
                    method = 'NEFT',
                    date=datetime.strptime(row.get('Voucher Date'), '%d-%m-%Y').date(),
                    type=voucher_type,
                    amount=decimal.Decimal(row.get('Net Amount')),
                    remark=row.get('Remarks')
                )

            payment.save()
        return redirect('admin-ledger')


@user_passes_test(is_admin, login_url='login')
def admin_import_discounts_view(request, model):
    if request.method == 'POST' and request.FILES.get('file13'):
        uploaded_file = request.FILES['file13']

        decoded_file = uploaded_file.read().decode('utf-8').splitlines()
        csv_reader = csv.DictReader(decoded_file)

        for row in csv_reader:
            invoice=row.get('Number')
            item=row.get('Item Description').strip()
            discount=row.get('Disc %')

            if discount == '0':
                continue

            try:
                product = models.Product.objects.get(item=item)
                if model == 'purchase':
                    purchase = models.Purchase.objects.get(inv=invoice)


                    try:
                        product = models.Batch_exp.objects.get(purchase=purchase, product=product)
                    except:
                        print(invoice,item,discount)
                else:
                    sales = models.Orders.objects.get(invoice=invoice)

                    try:
                        product = models.OrderProduct.objects.get(order=sales, product=product)
                    except:
                        print(invoice,item,discount)
            except:
                print(item)


            product.discount = discount
            product.save()

        return redirect('admin-batch')



# admin view the feedback
@user_passes_test(is_admin, login_url='login')
def view_feedback_view(request):
    feedbacks=models.Feedback.objects.all().order_by('-id')
    return render(request,'ecom/view_feedback.html',{'feedbacks':feedbacks})

@user_passes_test(is_admin, login_url='login')
def admin_import_purchase_view(request):
    if request.method == 'POST' and request.FILES.get('purchase_file'):
        try:
            import pandas as pd
            from datetime import datetime
            from django.db import transaction
            from decimal import Decimal

            # Read the Excel file with explicit engine specification
            file = request.FILES['purchase_file']
            if file.name.endswith('.xlsx'):
                df = pd.read_excel(file, engine='openpyxl')
            else:
                df = pd.read_excel(file, engine='xlrd')

            # Validate required columns
            required_columns = ['Date', 'Number', 'Party Name', 'Ref.Doc No.', 'Disc. Amount', 'Tax Amount', 'Net Amount']
            missing_columns = [col for col in required_columns if col not in df.columns]

            if missing_columns:
                messages.error(request, f"Missing required columns: {', '.join(missing_columns)}")
                return render(request, 'ecom/Admin/import_purchase.html')

            # Process data within a transaction
            with transaction.atomic():
                purchases_created = 0
                errors = []

                for index, row in df.iterrows():
                    try:
                        # Get or create supplier
                        party_name = row['Party Name']
                        try:
                            # Try to find supplier by name
                            supplier = models.Supplier.objects.filter(
                                name__iexact=party_name
                            ).first()

                            if not supplier:
                                # Create a new supplier with dummy data
                                supplier = models.Supplier.objects.create(
                                    name=party_name,
                                    address="Auto-created during import",
                                    mobile="",
                                    email="",
                                    place_of_supply="Karnataka"
                                )
                                messages.info(request, f"Created new supplier: {party_name} (row {index+2})")

                        except Exception as e:
                            errors.append(f"Error finding/creating supplier {party_name}: {str(e)} (row {index+2})")
                            continue

                        # Parse date
                        try:
                            if isinstance(row['Date'], str):
                                # Try different date formats with time components
                                try:
                                    # Format with time: dd-mm-yyyy HH:MM:SS
                                    date = datetime.strptime(row['Date'], '%d-%m-%Y %H:%M:%S').date()
                                except ValueError:
                                    try:
                                        # Format with time: yyyy-mm-dd HH:MM:SS
                                        date = datetime.strptime(row['Date'], '%Y-%m-%d %H:%M:%S').date()
                                    except ValueError:
                                        try:
                                            # Format with time: dd/mm/yyyy HH:MM:SS
                                            date = datetime.strptime(row['Date'], '%d/%m/%Y %H:%M:%S').date()
                                        except ValueError:
                                            try:
                                                # Format without time: dd-mm-yyyy
                                                date = datetime.strptime(row['Date'], '%d-%m-%Y').date()
                                            except ValueError:
                                                try:
                                                    # Format without time: yyyy-mm-dd
                                                    date = datetime.strptime(row['Date'], '%Y-%m-%d').date()
                                                except ValueError:
                                                    # Format without time: dd/mm/yyyy
                                                    date = datetime.strptime(row['Date'], '%d/%m/%Y').date()
                            else:
                                # If it's already a datetime object, extract just the date part
                                date = row['Date'].date() if hasattr(row['Date'], 'date') else row['Date']
                        except Exception as e:
                            errors.append(f"Invalid date format in row {index+2}: {str(e)}")
                            continue

                        # Get invoice number - extract the last part after the last '/'
                        try:
                            # For format like "DI/25-26/3", extract just the '3'
                            number_str = str(row['Number'])
                            if '/' in number_str:
                                # Split by '/' and get the last part
                                invoice_number = int(number_str.split('/')[-1])
                            else:
                                # If no '/', try to convert the whole string to int
                                invoice_number = int(number_str)
                        except (ValueError, TypeError):
                            errors.append(f"Invalid invoice number in row {index+2}: {row['Number']} - Expected format like 'DI/25-26/3' or a numeric value")
                            continue

                        # Check if purchase with this invoice already exists
                        ref_doc_no = str(row['Ref.Doc No.'])
                        if models.Purchase.objects.filter(invoice=ref_doc_no).exists():
                            errors.append(f"Purchase with reference document number {ref_doc_no} already exists (row {index+2})")
                            continue

                        # Parse amounts
                        try:
                            disc_amount = Decimal(str(row['Disc. Amount'] or 0))
                            tax_amount = Decimal(str(row['Tax Amount'] or 0))
                            net_amount = Decimal(str(row['Net Amount'] or 0))
                            total_amount = net_amount - tax_amount  # Total without tax
                        except (ValueError, InvalidOperation, TypeError) as e:
                            errors.append(f"Invalid amount format in row {index+2}: {str(e)}")
                            continue

                        # Create purchase
                        purchase = models.Purchase.objects.create(
                            supplier=supplier,
                            invoice=ref_doc_no,
                            inv=invoice_number,
                            date=date,
                            total=total_amount,
                            amount=net_amount,
                            paid=False
                        )

                        purchases_created += 1

                    except Exception as e:
                        errors.append(f"Error processing row {index+2}: {str(e)}")

                # Report results
                if purchases_created > 0:
                    messages.success(request, f"Successfully imported {purchases_created} purchases.")

                if errors:
                    # Limit the number of errors shown to avoid overwhelming the user
                    max_errors_to_show = 10
                    error_message = "<br>".join(errors[:max_errors_to_show])
                    if len(errors) > max_errors_to_show:
                        error_message += f"<br>...and {len(errors) - max_errors_to_show} more errors."
                    messages.warning(request, f"Encountered {len(errors)} errors during import: <br>{error_message}", extra_tags='safe')

                return redirect('admin-batch')

        except Exception as e:
            messages.error(request, f"Error importing purchase data: {str(e)}")
            return render(request, 'ecom/Admin/import_purchase.html')

    return render(request, 'ecom/Admin/import_purchase.html')
