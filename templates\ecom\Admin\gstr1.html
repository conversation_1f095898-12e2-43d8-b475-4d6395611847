{% extends 'ecom/Admin/admin_base.html' %}
{% load static %}
{% block content %}

<div class="container-fluid py-4">
  <div class="row justify-content-center">
    <div class="col-12">
      <div class="card shadow-sm">
        <div class="card-header bg-primary text-white">
          <h5 class="mb-0">GSTR-1 Report</h5>
        </div>
        <div class="card-body">
          <form method="post" class="row g-3 align-items-end mb-4">
            {% csrf_token %}
            <div class="col-md-2">
              <label for="month" class="form-label">Month</label>
              <select id="month" name="month" class="form-select">
                <option value="01">January</option>
                <option value="02">February</option>
                <option value="03">March</option>
                <option value="04">April</option>
                <option value="05">May</option>
                <option value="06">June</option>
                <option value="07">July</option>
                <option value="08">August</option>
                <option value="09">September</option>
                <option value="10">October</option>
                <option value="11">November</option>
                <option value="12">December</option>
              </select>
            </div>
            <div class="col-md-2">
              <label for="year" class="form-label">Year</label>
              <select id="year" name="year" class="form-select">
                {% for year in years %}
                  <option value="{{ year }}">{{ year }}</option>
                {% endfor %}
              </select>
            </div>
            <div class="col-md-2">
              <div class="form-check">
                <input type="checkbox" id="toggle" name="toggle" value="false" class="form-check-input">
                <label for="B2C" class="form-check-label">B2C</label>
              </div>
            </div>
            <div class="col-md-2">
              <div class="form-check">
                <input type="checkbox" id="print" name="print" value="false" class="form-check-input">
                <label for="print" class="form-check-label">Print</label>
              </div>
            </div>
            <div class="col-md-2">
              <button type="submit" name="action" value="generate_report" class="btn btn-primary w-100">Generate Report</button>
            </div>
            <div class="col-md-2">
              <button type="submit" name="generate_hsn" value="true" class="btn btn-success w-100">Generate HSN</button>
            </div>
          </form>

          <div class="table-responsive">
            {% if is_hsn_report %}
            <!-- HSN Report Table -->
            <table class="table table-hover table-bordered">
              <thead class="table-light">
                <tr>
                  <th>HSN</th>
                  <th>Item</th>
                  <th>UQC</th>
                  <th>Total Quantity</th>
                  <th>Total Value (with tax)</th>
                  <th>Total Taxable Value (without tax)</th>
                  <th>Integrated Tax</th>
                  <th>Central Tax</th>
                  <th>State/UT Tax</th>
                  <th>Total Cess</th>
                </tr>
              </thead>
              <tbody>
                {% for entry in hsn_report %}
                <tr>
                  <td>{{ entry.0 }}</td>
                  <td class="text-left">{{ entry.1 }}</td>
                  <td>{{ entry.2 }}</td>
                  <td>{{ entry.3 }}</td>
                  <td>{{ entry.4|floatformat:2 }}</td>
                  <td>{{ entry.5|floatformat:2 }}</td>
                  <td>{{ entry.6|floatformat:2 }}</td>
                  <td>{{ entry.7|floatformat:2 }}</td>
                  <td>{{ entry.8|floatformat:2 }}</td>
                  <td>{{ entry.9|floatformat:2 }}</td>
                </tr>
                {% endfor %}
              </tbody>
            </table>
            {% else %}
            <!-- Regular GSTR1 Report Table -->
            <table class="table table-hover table-bordered">
              <thead class="table-light">
                <tr>
                  <th>Date</th>
                  <th>Invoice no.</th>
                  <th>Party Name</th>
                  <th>GSTIN</th>
                  <th>Total Quantity</th>
                  <th>Total Taxable amount</th>
                  <th>IGST Rate</th>
                  <th>IGST Amount</th>
                  <th>CGST Rate</th>
                  <th>CGST Amount</th>
                  <th>SGST Rate</th>
                  <th>SGST Amount</th>
                  <th>Total Invoice Value</th>
                </tr>
              </thead>
              <tbody>
                {% for entry in orders %}
                <tr>
                  <td>{{ entry.1 }}</td>
                  <td>{{ entry.2 }}</td>
                  <td>{{ entry.3 }}</td>
                  <td>{{ entry.4 }}</td>
                  <td>{{ entry.6 }}</td>
                  <td>{{ entry.7 }}</td>
                  <td>{{ entry.8 }}</td>
                  <td>{{ entry.9 }}</td>
                  <td>{{ entry.10 }}</td>
                  <td>{{ entry.11 }}</td>
                  <td>{{ entry.12 }}</td>
                  <td>{{ entry.13 }}</td>
                  <td>{{ entry.14 }}</td>
                </tr>
                {% endfor %}
              </tbody>
            </table>
            {% endif %}
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

{% endblock content %}
