{% extends 'ecom/Admin/admin_base.html' %}

{% block title %}Purchases | Inventory System{% endblock %}

{% block content %}
<div class="container-fluid pt-1 mt-1">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0">Purchases</h1>
        <a href="{% url 'purchase_create' %}" class="btn btn-primary">
            <i class="fas fa-plus me-1"></i> New Purchase
        </a>
    </div>

    <div class="card shadow-sm">
        <div class="card-header bg-light py-3">
            <div class="row g-3">
                <div class="col-12 col-md-8">
                    <form method="get" class="d-flex gap-2">
                        <input type="text" name="search" class="form-control" placeholder="Search by invoice or supplier..."
                               value="{{ request.GET.search|default:'' }}">
                        <button type="submit" class="btn btn-outline-primary">
                            <i class="fas fa-search me-1"></i> Search
                        </button>
                    </form>
                </div>
                <div class="col-12 col-md-4">
                    <div class="d-flex gap-2 justify-content-md-end">
                        <select name="supplier" id="supplier_filter" class="form-select">
                            <option value="">All Suppliers</option>
                            {% for supplier in suppliers %}
                                <option value="{{ supplier.id }}" {% if supplier.id|stringformat:"s" == selected_supplier %}selected{% endif %}>{{ supplier.name }}</option>
                            {% endfor %}
                        </select>
                        <select name="financial_year" id="financial_year" class="form-select">
                            <option value="">All Years</option>
                            {% for year in financial_years %}
                                <option value="{{ year }}" {% if year|stringformat:"s" == selected_year %}selected{% endif %}>{{ year }}</option>
                            {% endfor %}
                        </select>
                        <select name="status" id="status_filter" class="form-select">
                            <option value="">All Status</option>
                            <option value="paid" {% if status == 'paid' %}selected{% endif %}>Paid</option>
                            <option value="unpaid" {% if status == 'unpaid' %}selected{% endif %}>Unpaid</option>
                        </select>
                    </div>
                </div>
            </div>
        </div>
        <div class="card-body p-0">
            <div class="table-responsive">
                <table class="table table-hover table-striped align-middle mb-0">
                    <thead class="table-light">
                        <tr>
                            <th class="px-3">Invoice #</th>
                            <th>Supplier</th>
                            <th>Date</th>
                            <th class="text-end">Total Amount</th>
                            <th>Status</th>
                            <th class="text-center">Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% if purchases %}
                            {% for purchase in purchases %}
                            <tr>
                                <td class="px-3">
                                    <a href="{% url 'purchase_detail' purchase.pk %}" class="text-decoration-none">
                                        {{ purchase.invoice|default:purchase.inv }}
                                    </a>
                                    {% if purchase.invoice and purchase.inv %}
                                    <small class="d-block text-muted">ID: {{ purchase.inv }}</small>
                                    {% endif %}
                                </td>
                                <td>{{ purchase.supplier.name }}</td>
                                <td>{{ purchase.date|date:"M d, Y" }}</td>
                                <td class="text-end">₹{{ purchase.amount|floatformat:2 }}</td>
                                <td>
                                    {% if purchase.paid %}
                                        <span class="badge bg-success">Paid</span>
                                    {% else %}
                                        <span class="badge bg-danger">Unpaid</span>
                                    {% endif %}
                                </td>
                                <td class="text-center">
                                    <div class="btn-group">
                                        <a href="{% url 'purchase_detail' purchase.pk %}" class="btn btn-sm btn-outline-primary" title="View Details">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <a href="{% url 'purchase_update' purchase.pk %}" class="btn btn-sm btn-outline-secondary" title="Edit">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <a href="{% url 'purchase_toggle_paid' purchase.pk %}" class="btn btn-sm btn-outline-{% if purchase.paid %}warning{% else %}success{% endif %}"
                                           title="{% if purchase.paid %}Mark as Unpaid{% else %}Mark as Paid{% endif %}">
                                            <i class="fas fa-{% if purchase.paid %}undo{% else %}check{% endif %}"></i>
                                        </a>
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                        {% else %}
                            <tr>
                                <td colspan="6" class="text-center py-5">
                                    <div class="text-muted">
                                        <i class="fas fa-box-open fa-2x mb-3"></i>
                                        <p class="mb-0">No purchases found.</p>
                                    </div>
                                </td>
                            </tr>
                        {% endif %}
                    </tbody>
                </table>
            </div>
        </div>
        <div class="card-footer bg-light py-3">
            <div class="d-flex flex-column flex-md-row justify-content-between align-items-center gap-3">
                <p class="mb-0 text-muted">Showing {{ purchases.count }} purchase{{ purchases.count|pluralize }}</p>

                <!-- Pagination -->
                {% if purchases.has_other_pages %}
                <nav aria-label="Page navigation">
                    <ul class="pagination pagination-sm mb-0">
                        {% if purchases.has_previous %}
                        <li class="page-item">
                            <a class="page-link" href="?page={{ purchases.previous_page_number }}&search={{ request.GET.search|default:'' }}&supplier={{ request.GET.supplier|default:'' }}&financial_year={{ request.GET.financial_year|default:'' }}&status={{ request.GET.status|default:'' }}">
                                <i class="fas fa-chevron-left"></i>
                            </a>
                        </li>
                        {% else %}
                        <li class="page-item disabled">
                            <a class="page-link" href="#" tabindex="-1">
                                <i class="fas fa-chevron-left"></i>
                            </a>
                        </li>
                        {% endif %}

                        {% for i in purchases.paginator.page_range %}
                            {% if purchases.number == i %}
                            <li class="page-item active">
                                <a class="page-link" href="#">{{ i }}</a>
                            </li>
                            {% else %}
                            <li class="page-item">
                                <a class="page-link" href="?page={{ i }}&search={{ request.GET.search|default:'' }}&supplier={{ request.GET.supplier|default:'' }}&financial_year={{ request.GET.financial_year|default:'' }}&status={{ request.GET.status|default:'' }}">{{ i }}</a>
                            </li>
                            {% endif %}
                        {% endfor %}

                        {% if purchases.has_next %}
                        <li class="page-item">
                            <a class="page-link" href="?page={{ purchases.next_page_number }}&search={{ request.GET.search|default:'' }}&supplier={{ request.GET.supplier|default:'' }}&financial_year={{ request.GET.financial_year|default:'' }}&status={{ request.GET.status|default:'' }}">
                                <i class="fas fa-chevron-right"></i>
                            </a>
                        </li>
                        {% else %}
                        <li class="page-item disabled">
                            <a class="page-link" href="#" tabindex="-1">
                                <i class="fas fa-chevron-right"></i>
                            </a>
                        </li>
                        {% endif %}
                    </ul>
                </nav>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    $(document).ready(function() {
        // Add loading state to filters
        $('#supplier_filter, #financial_year, #status_filter').change(function() {
            var $card = $('.card');
            $card.addClass('opacity-50');

            var supplier = $('#supplier_filter').val();
            var financial_year = $('#financial_year').val();
            var status = $('#status_filter').val();
            var search = $('input[name="search"]').val();

            window.location.href = "{% url 'purchase_list' %}?supplier=" + supplier +
                                   "&financial_year=" + financial_year +
                                   "&status=" + status +
                                   "&search=" + search;
        });
    });
</script>
{% endblock %}